# Aureus Alliance Holdings Bot - Environment Configuration
# Copy this file to .env and fill in your actual values

# Bot Configuration
# Set NODE_ENV to 'development' for dev bot, 'production' for live bot
NODE_ENV=development

# Production Bot (Live) - DO NOT USE DURING DEVELOPMENT
BOT_TOKEN_PRODUCTION=7858706839:AAFRXBSlREW0wPvIyI57uFpHfYopi2CY464
BOT_USERNAME_PRODUCTION=AureusAllianceBot

# Development Bot (Testing) - Safe for development and testing
BOT_TOKEN_DEVELOPMENT=8165881275:AAGCpFnHR-mYUeawUTyfbOa1jrDz5w2NWtQ
BOT_USERNAME_DEVELOPMENT=AureusAllianceDevBot

# Database Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Database Connection (if using direct PostgreSQL)
SUPABASE_DB_HOST=your_db_host
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=your_db_name
SUPABASE_DB_USER=your_db_user
SUPABASE_DB_PASSWORD=your_db_password

# Security
JWT_SECRET=your_jwt_secret_here

# Admin Configuration
ADMIN_USERNAME=TTTFOUNDER
