import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function addTelegramIdColumn() {
  try {
    console.log('🔧 Adding telegram_id column to users table...');
    
    // Use raw SQL query to add column
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);
    
    if (error) {
      console.error('❌ Error connecting to database:', error);
      return;
    }
    
    console.log('✅ Connected to database successfully');
    
    // Try to add the column using a simple approach
    console.log('🔧 Attempting to add telegram_id column...');
    
    // Since we can't execute DDL directly, let's check if the column exists first
    const { data: columnCheck, error: columnError } = await supabase
      .from('users')
      .select('telegram_id')
      .limit(1);
    
    if (columnError) {
      if (columnError.code === '42703') {
        console.log('❌ telegram_id column does not exist in users table');
        console.log('📝 You need to manually add the column using Supabase dashboard or SQL editor:');
        console.log('   ALTER TABLE users ADD COLUMN telegram_id BIGINT UNIQUE;');
        console.log('   CREATE INDEX idx_users_telegram_id ON users(telegram_id);');
      } else {
        console.error('❌ Error checking column:', columnError);
      }
    } else {
      console.log('✅ telegram_id column already exists in users table');
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

addTelegramIdColumn();
