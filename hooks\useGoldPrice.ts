import { useState, useEffect } from 'react';

interface GoldPriceData {
  price: number;
  lastUpdated: string;
  isLoading: boolean;
  error: string | null;
}

export const useGoldPrice = () => {
  const [goldPrice, setGoldPrice] = useState<GoldPriceData>({
    price: 100000, // Default fallback price
    lastUpdated: '',
    isLoading: false,
    error: null,
  });

  const fetchGoldPrice = async () => {
    setGoldPrice(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Try multiple gold price APIs in order of preference
      const apis = [
        {
          url: 'https://api.coindesk.com/v1/bpi/currentprice.json',
          parser: (data: any) => {
            // CoinDesk Bitcoin Price Index - not gold, but reliable for testing
            // This is just a fallback for testing - we'll use a static gold price
            throw new Error('Bitcoin API, not gold');
          }
        },
        {
          url: 'https://api.exchangerate-api.com/v4/latest/USD',
          parser: (data: any) => {
            // This doesn't have gold prices either, just for testing connectivity
            throw new Error('Currency API, not gold');
          }
        }
      ];

      // For now, use a realistic static price with some variation
      // In production, you would integrate with a proper gold price API
      const basePrice = 100000; // $100k per kg base price
      const variation = Math.sin(Date.now() / (1000 * 60 * 60 * 24)) * 5000; // Daily variation
      const currentPrice = Math.round(basePrice + variation);

      setGoldPrice({
        price: currentPrice,
        lastUpdated: new Date().toLocaleString(),
        isLoading: false,
        error: null,
      });

      console.log(`💰 Using simulated gold price: $${currentPrice.toLocaleString()}/kg`);

    } catch (error) {
      console.warn('Failed to fetch live gold price, using fallback:', error);
      setGoldPrice(prev => ({
        ...prev,
        isLoading: false,
        error: 'Using simulated price',
      }));
    }
  };

  useEffect(() => {
    // Fetch gold price on component mount
    fetchGoldPrice();
    
    // Set up interval to fetch every 5 minutes
    const interval = setInterval(fetchGoldPrice, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    ...goldPrice,
    refetch: fetchGoldPrice,
  };
};
