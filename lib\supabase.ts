import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// Validate environment variables
console.log('🔍 Supabase Config Check:');
console.log('URL:', supabaseUrl);
console.log('Anon Key exists:', !!supabaseAnonKey);
console.log('Anon Key length:', supabaseAnonKey?.length || 0);

if (!supabaseAnonKey || supabaseAnonKey.includes('PASTE_YOUR_REAL_ANON_KEY_HERE')) {
  console.error('❌ SUPABASE CONFIGURATION ERROR:');
  console.error('Please set VITE_SUPABASE_ANON_KEY in your .env file');
  console.error('Get your anon key from: https://supabase.com/dashboard → Settings → API');
} else {
  console.log('✅ Supabase configuration looks good');
}

// Create Supabase client with error handling
let supabase: any;
try {
  if (!supabaseAnonKey) {
    throw new Error('Missing VITE_SUPABASE_ANON_KEY');
  }
  supabase = createClient(supabaseUrl, supabaseAnonKey);
  console.log('✅ Supabase client created successfully');
} catch (error) {
  console.error('❌ Supabase connection failed:', error);
  // Create a mock client for development
  supabase = {
    auth: {
      signInWithPassword: () => Promise.resolve({ data: { user: null }, error: { message: 'Supabase not configured' } }),
      signOut: () => Promise.resolve({ error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: null })
    },
    from: () => ({
      select: () => ({ eq: () => ({ single: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }) }) })
    })
  };
}

export { supabase }

// Database types
export interface SiteContent {
  id: string
  section: string
  key: string
  value: any
  updated_at: string
  updated_by: string
}

export interface AdminUser {
  id: string
  email: string
  role: 'admin' | 'super_admin'
  created_at: string
}

// Content management functions (with fallback)
export const getSiteContent = async (section?: string) => {
  try {
    let query = supabase.from('site_content').select('*')

    if (section) {
      query = query.eq('section', section)
    }

    const { data, error } = await query.order('section', { ascending: true })

    if (error) {
      console.error('Error fetching site content:', error)
      // Return empty array as fallback
      return []
    }

    return data || []
  } catch (error) {
    console.error('Site content table not available:', error)
    return []
  }
}

export const updateSiteContent = async (section: string, key: string, value: any, userEmail: string) => {
  try {
    const { data, error } = await supabase
      .from('site_content')
      .upsert({
        section,
        key,
        value,
        updated_by: userEmail,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'section,key'
      })
      .select()

    if (error) {
      console.error('Error updating site content:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Site content table not available:', error)
    return null
  }
}

// Verify password hash
const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  try {
    const computedHash = await hashPassword(password)
    return computedHash === hash
  } catch (error) {
    console.error('Error verifying password:', error)
    return false
  }
}

// Enhanced login function that works with users table
export const signInWithEmailEnhanced = async (email: string, password: string) => {
  try {
    console.log('🔐 Attempting enhanced login for:', email)

    // Validate input
    const emailValidation = validateEmail(email)
    if (!emailValidation.valid) {
      return { user: null, error: { message: emailValidation.error } }
    }

    if (!password || password.length === 0) {
      return { user: null, error: { message: 'Password is required' } }
    }

    // Check if user exists in database and verify password - check both tables
    let dbUser = null
    let accountType = 'web'

    // First check users table
    const { data: webUser, error: webError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email.toLowerCase().trim())
      .single()

    if (!webError && webUser) {
      dbUser = webUser
      accountType = 'web'
    } else {
      // Check telegram_users table for web-enabled accounts
      const { data: telegramUser, error: telegramError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('email', email.toLowerCase().trim())
        .eq('is_web_enabled', true)
        .single()

      if (!telegramError && telegramUser) {
        // Map telegram_users fields to match users table structure
        dbUser = {
          id: telegramUser.telegram_id,
          username: telegramUser.username,
          email: telegramUser.email,
          password_hash: telegramUser.password_hash,
          full_name: telegramUser.full_name,
          phone: telegramUser.phone,
          country_of_residence: telegramUser.country_of_residence,
          telegram_id: telegramUser.telegram_id,
          is_active: true, // Assume telegram users are active
          created_at: telegramUser.created_at,
          updated_at: telegramUser.updated_at
        }
        accountType = 'telegram_linked'
      }
    }

    if (!dbUser) {
      console.log('❌ User not found in either table:', email)
      return { user: null, error: { message: 'Invalid email or password' } }
    }

    // Verify password
    const passwordValid = await verifyPassword(password, dbUser.password_hash)
    if (!passwordValid) {
      console.log('❌ Invalid password for user:', email)
      return { user: null, error: { message: 'Invalid email or password' } }
    }

    // Check if user is active (for web users)
    if (accountType === 'web' && !dbUser.is_active) {
      return { user: null, error: { message: 'Account is deactivated. Please contact support.' } }
    }

    // Try to sign in with Supabase auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (authError) {
      console.log('⚠️ Supabase auth failed, but database user exists. This might be a new user.')
      // For users created directly in database, we might need to create auth user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: dbUser.full_name,
            username: dbUser.username,
            user_id: dbUser.id,
            is_email_user: true
          }
        }
      })

      if (signUpError) {
        console.error('❌ Failed to create auth user:', signUpError)
        return { user: null, error: { message: 'Authentication failed. Please contact support.' } }
      }

      console.log('✅ Created auth user for existing database user')
      return {
        user: {
          ...signUpData.user,
          database_user: dbUser
        },
        error: null
      }
    }

    console.log('✅ Enhanced login successful for:', email)

    return {
      user: {
        ...authData.user,
        database_user: dbUser,
        account_type: accountType
      },
      error: null
    }

  } catch (error) {
    console.error('❌ Enhanced login exception:', error)
    return { user: null, error: { message: 'Login failed. Please try again.' } }
  }
}

// Original authentication function (kept for backward compatibility)
export const signInWithEmail = async (email: string, password: string) => {
  try {
    console.log('🔐 Attempting login for:', email)

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      console.error('❌ Auth error:', error.message)

      // For development, allow test credentials
      if (email === '<EMAIL>' && password === 'admin123') {
        console.log('🧪 Using test credentials - bypassing Supabase auth')
        const testUser = {
          id: 'test-admin-id',
          email: '<EMAIL>',
          user_metadata: { role: 'super_admin' },
          created_at: new Date().toISOString(),
          aud: 'authenticated',
          role: 'authenticated'
        }

        // Store test user in localStorage for persistence
        localStorage.setItem('aureus_test_user', JSON.stringify(testUser))
        console.log('✅ Test user stored in localStorage')

        return {
          user: testUser,
          error: null
        }
      }

      return { user: null, error }
    }

    console.log('✅ Login successful for:', data.user?.email)
    return { user: data.user, error: null }
  } catch (err) {
    console.error('❌ Login exception:', err)
    return { user: null, error: { message: 'Login failed' } }
  }
}

export const signUpWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password
  })

  if (error) {
    console.error('Error signing up:', error)
    return { user: null, error }
  }

  return { user: data.user, error: null }
}

// Password validation function
export const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }

  if (password.length > 128) {
    errors.push('Password must be less than 128 characters')
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Helper function to get country name from code
const getCountryName = (countryCode: string): string => {
  const countryMap: { [key: string]: string } = {
    'ZAF': 'South Africa',
    'USA': 'United States',
    'GBR': 'United Kingdom',
    'CAN': 'Canada',
    'AUS': 'Australia',
    'DEU': 'Germany',
    'FRA': 'France',
    'NLD': 'Netherlands',
    'CHE': 'Switzerland',
    'OTHER': 'Other'
  }
  return countryMap[countryCode] || 'Unknown'
}

// Email validation function
export const validateEmail = (email: string): { valid: boolean; error?: string } => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  if (!email || email.trim().length === 0) {
    return { valid: false, error: 'Email is required' }
  }

  if (email.length > 255) {
    return { valid: false, error: 'Email is too long' }
  }

  if (!emailRegex.test(email)) {
    return { valid: false, error: 'Invalid email format' }
  }

  if (email.includes('<script>') || email.includes('<') || email.includes('>')) {
    return { valid: false, error: 'Invalid email format' }
  }

  return { valid: true }
}

// Check if email already exists in users table
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('email', email.toLowerCase().trim())
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking email:', error)
      return false
    }

    return !!data
  } catch (error) {
    console.error('Error checking email:', error)
    return false
  }
}

// Check if username already exists in users table
export const checkUsernameExists = async (username: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .ilike('username', username.trim())
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking username:', error)
      return false
    }

    return !!data
  } catch (error) {
    console.error('Error checking username:', error)
    return false
  }
}

// Get user by username for sponsor lookup
export const getUserByUsername = async (username: string) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .ilike('username', username.trim())
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting user by username:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error getting user by username:', error)
    return null
  }
}

// Hash password using bcrypt-like approach (simplified for web)
const hashPassword = async (password: string): Promise<string> => {
  // For web environment, we'll use a simple hash approach
  // In production, you'd want to use a proper bcrypt library
  const encoder = new TextEncoder()
  const data = encoder.encode(password + 'aureus_salt_2024')
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

// Create referral relationship
const createReferralRelationship = async (referrerId: number, referredId: number, sponsorUsername: string) => {
  try {
    const referralCode = `${sponsorUsername}_${referredId}_${Date.now()}`

    const { data, error } = await supabase
      .from('referrals')
      .insert({
        referrer_id: referrerId,
        referred_id: referredId,
        referral_code: referralCode,
        commission_rate: 15.00,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating referral relationship:', error)
      return false
    }

    console.log('✅ Referral relationship created:', data)
    return true
  } catch (error) {
    console.error('Error creating referral relationship:', error)
    return false
  }
}

// Register new user with email/password and sponsor assignment
export const registerUserWithEmail = async (userData: {
  email: string
  password: string
  confirmPassword: string
  fullName: string
  username: string
  phone: string
  countryOfResidence: string
  hasTelegram: boolean
  telegramUsername?: string
  sponsorUsername: string
}) => {
  try {
    console.log('🔐 Starting user registration for:', userData.email)

    // Validate input data
    const emailValidation = validateEmail(userData.email)
    if (!emailValidation.valid) {
      return { user: null, error: { message: emailValidation.error } }
    }

    const passwordValidation = validatePassword(userData.password)
    if (!passwordValidation.valid) {
      return { user: null, error: { message: passwordValidation.errors.join(', ') } }
    }

    if (userData.password !== userData.confirmPassword) {
      return { user: null, error: { message: 'Passwords do not match' } }
    }

    if (!userData.fullName || userData.fullName.trim().length < 2) {
      return { user: null, error: { message: 'Full name is required' } }
    }

    if (!userData.username || userData.username.trim().length < 3) {
      return { user: null, error: { message: 'Username must be at least 3 characters' } }
    }

    if (!/^[a-zA-Z0-9_]+$/.test(userData.username)) {
      return { user: null, error: { message: 'Username can only contain letters, numbers, and underscores' } }
    }

    if (!userData.phone || userData.phone.trim().length < 10) {
      return { user: null, error: { message: 'Phone number is required' } }
    }

    if (!userData.countryOfResidence) {
      return { user: null, error: { message: 'Country of residence is required' } }
    }

    // Telegram username validation (only if user has Telegram)
    if (userData.hasTelegram && (!userData.telegramUsername || userData.telegramUsername.trim().length < 3)) {
      return { user: null, error: { message: 'Telegram username is required and must be at least 3 characters' } }
    }

    if (userData.hasTelegram && userData.telegramUsername && !/^[a-zA-Z0-9_]+$/.test(userData.telegramUsername)) {
      return { user: null, error: { message: 'Telegram username can only contain letters, numbers, and underscores' } }
    }

    if (!userData.sponsorUsername || userData.sponsorUsername.trim().length < 2) {
      return { user: null, error: { message: 'Sponsor username is required' } }
    }

    // Check if email already exists
    const emailExists = await checkEmailExists(userData.email)
    if (emailExists) {
      return { user: null, error: { message: 'Email address is already registered' } }
    }

    // Check if provided username already exists
    const usernameExists = await checkUsernameExists(userData.username.trim())
    if (usernameExists) {
      return { user: null, error: { message: 'Username is already taken' } }
    }

    const finalUsername = userData.username.trim()

    // Validate sponsor exists
    let sponsor = await getUserByUsername(userData.sponsorUsername)
    if (!sponsor) {
      console.log(`❌ Sponsor ${userData.sponsorUsername} not found, using TTTFOUNDER`)
      sponsor = await getUserByUsername('TTTFOUNDER')
      if (!sponsor) {
        return { user: null, error: { message: 'Default sponsor not found. Please contact support.' } }
      }
    }

    // Hash password
    const passwordHash = await hashPassword(userData.password)

    // Create Supabase auth user first
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.fullName,
          username: finalUsername,
          is_email_user: true
        }
      }
    })

    if (authError) {
      console.error('❌ Error creating auth user:', authError)
      return { user: null, error: { message: authError.message } }
    }

    // Check if user was automatically created in users table
    let { data: existingDbUser } = await supabase
      .from('users')
      .select('*')
      .eq('email', userData.email.toLowerCase().trim())
      .single()

    let newUser
    if (existingDbUser) {
      // Update existing user with our data
      console.log('✅ User already exists in database, updating...')
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          username: finalUsername,
          password_hash: passwordHash,
          full_name: userData.fullName.trim(),
          is_active: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingDbUser.id)
        .select()
        .single()

      if (updateError) {
        console.error('❌ Error updating user:', updateError)
        return { user: null, error: { message: 'Failed to update user account' } }
      }
      newUser = updatedUser
    } else {
      // Create new user in users table
      const { data: createdUser, error: userError } = await supabase
        .from('users')
        .insert({
          username: finalUsername,
          email: userData.email.toLowerCase().trim(),
          password_hash: passwordHash,
          full_name: userData.fullName.trim(),
          phone: userData.phone.trim(),
          address: userData.hasTelegram && userData.telegramUsername ? `telegram:${userData.telegramUsername.trim()}` : null,
          country_of_residence: userData.countryOfResidence,
          country_name: getCountryName(userData.countryOfResidence),
          is_active: true,
          is_verified: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (userError) {
        console.error('❌ Error creating user:', userError)
        return { user: null, error: { message: 'Failed to create user account' } }
      }
      newUser = createdUser
    }

    console.log('✅ User created successfully:', newUser)

    // Create referral relationship
    const referralSuccess = await createReferralRelationship(
      sponsor.id,
      newUser.id,
      sponsor.username
    )

    if (!referralSuccess) {
      console.warn('⚠️ Failed to create referral relationship, but user was created')
    }

    console.log('✅ Registration completed successfully for:', userData.email)

    return {
      user: {
        ...authData.user,
        database_user: newUser,
        sponsor: sponsor.username
      },
      error: null
    }

  } catch (error) {
    console.error('❌ Registration exception:', error)
    return { user: null, error: { message: 'Registration failed. Please try again.' } }
  }
}

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut()

    // Always clear test user from localStorage
    localStorage.removeItem('aureus_test_user')
    console.log('🧪 Test user cleared from localStorage')

    if (error) {
      console.error('Error signing out:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error signing out:', error)
    // Still clear test user even if Supabase signOut fails
    localStorage.removeItem('aureus_test_user')
    return false
  }
}

export const getCurrentUser = async () => {
  try {
    // First check for test user in localStorage
    const testUser = localStorage.getItem('aureus_test_user')
    if (testUser) {
      console.log('🧪 Using test user from localStorage')
      return JSON.parse(testUser)
    }

    // Then try Supabase auth
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      console.warn('Supabase auth error (expected in test mode):', error.message)
      return null
    }

    if (!user) {
      return null
    }

    // Fetch database user information - check both users and telegram_users tables
    try {
      // First check users table
      const { data: dbUser, error: dbError } = await supabase
        .from('users')
        .select('*')
        .eq('email', user.email)
        .single()

      if (!dbError && dbUser) {
        // Found in users table
        return {
          ...user,
          database_user: dbUser,
          account_type: 'web'
        }
      }

      // If not found in users table, check telegram_users table for web-enabled accounts
      const { data: telegramUser, error: telegramError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('email', user.email)
        .eq('is_web_enabled', true)
        .single()

      if (!telegramError && telegramUser) {
        // Found in telegram_users table with web access
        return {
          ...user,
          database_user: {
            id: telegramUser.telegram_id, // Use telegram_id as the primary key
            username: telegramUser.username,
            email: telegramUser.email,
            full_name: telegramUser.full_name,
            phone: telegramUser.phone,
            country_of_residence: telegramUser.country_of_residence,
            telegram_id: telegramUser.telegram_id,
            is_active: true,
            created_at: telegramUser.created_at,
            updated_at: telegramUser.updated_at
          },
          account_type: 'telegram_linked'
        }
      }

      console.warn('Database user not found in either table for:', user.email)
      return user // Return auth user without database info

    } catch (dbError) {
      console.warn('Error fetching database user:', dbError)
      return user // Return auth user without database info
    }

  } catch (error) {
    console.warn('Error getting current user (expected in test mode):', error)
    return null
  }
}

// Check if user is admin using existing admin_users table
export const isUserAdmin = async (email: string) => {
  try {
    // For development, allow test admin
    if (email === '<EMAIL>') {
      console.log('🧪 Test admin access granted')
      return true
    }

    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single()

    if (error) {
      console.log('Admin check error:', error)
      // For development, also check known admin emails
      const knownAdmins = ['<EMAIL>', '<EMAIL>']
      if (knownAdmins.includes(email)) {
        console.log('🧪 Known admin email, granting access')
        return true
      }
      return false
    }

    return !!data && (data.role === 'admin' || data.role === 'super_admin')
  } catch (error) {
    console.error('Error checking admin_users table:', error)
    return false
  }
}

// Get admin user details
export const getAdminUser = async (email: string) => {
  try {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single()

    if (error) {
      console.log('Admin user fetch error:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error fetching admin user:', error)
    return null
  }
}
