// API endpoint to create crypto payment transactions
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      amount,
      shares_to_purchase,
      network,
      currency,
      sender_wallet,
      receiver_wallet,
      screenshot_url,
      status = 'pending',
      transaction_notes
    } = req.body;

    // Validate required fields
    if (!amount || !shares_to_purchase || !network || !currency || !sender_wallet || !receiver_wallet || !screenshot_url) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Get user from session/auth (you'll need to implement this based on your auth system)
    // For now, we'll use a placeholder - you should get this from the authenticated user
    const userId = req.headers.authorization ? 1 : null; // Replace with actual user ID extraction

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get current active phase
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError || !currentPhase) {
      return res.status(400).json({ error: 'No active investment phase found' });
    }

    // Create payment transaction record
    const paymentData = {
      user_id: userId,
      amount: parseFloat(amount),
      shares_to_purchase: parseInt(shares_to_purchase),
      network: network,
      currency: currency,
      sender_wallet: sender_wallet,
      receiver_wallet: receiver_wallet,
      screenshot_url: screenshot_url,
      status: status,
      transaction_notes: transaction_notes || null,
      phase_id: currentPhase.id,
      created_at: new Date().toISOString()
    };

    const { data: payment, error: paymentError } = await supabase
      .from('crypto_payment_transactions')
      .insert([paymentData])
      .select()
      .single();

    if (paymentError) {
      console.error('Error creating payment:', paymentError);
      return res.status(500).json({ error: 'Failed to create payment transaction' });
    }

    // If this is a bank transfer, also create a record in aureus_share_purchases with pending status
    if (network === 'BANK_TRANSFER') {
      const sharesPurchaseData = {
        user_id: userId,
        phase_id: currentPhase.id,
        shares_purchased: parseInt(shares_to_purchase),
        amount_paid: parseFloat(amount),
        payment_method: 'bank_transfer',
        payment_status: 'pending',
        transaction_reference: payment.id,
        created_at: new Date().toISOString()
      };

      const { error: sharesError } = await supabase
        .from('aureus_share_purchases')
        .insert([sharesPurchaseData]);

      if (sharesError) {
        console.error('Error creating shares purchase record:', sharesError);
        // Don't fail the whole transaction, just log the error
      }
    }

    res.status(201).json({
      success: true,
      payment: payment,
      message: 'Payment transaction created successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
