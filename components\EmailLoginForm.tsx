import React, { useState } from 'react'
import { signInWithEmailEnhanced } from '../lib/supabase'

interface EmailLoginFormProps {
  onLoginSuccess: (user: any) => void
  onSwitchToRegister: () => void
}

export const EmailLoginForm: React.FC<EmailLoginFormProps> = ({
  onLoginSuccess,
  onSwitchToRegister
}) => {
  const [loginMode, setLoginMode] = useState<'email' | 'telegram'>('email')
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    telegramId: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [generalError, setGeneralError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [telegramVerification, setTelegramVerification] = useState<{
    verified: boolean
    telegramUser: any
    needsProfileCompletion: boolean
    loading: boolean
    error: string
  }>({
    verified: false,
    telegramUser: null,
    needsProfileCompletion: false,
    loading: false,
    error: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear field-specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }

    // Clear general error when user starts typing
    if (generalError) {
      setGeneralError('')
    }

    // Auto-verify Telegram ID when user finishes typing
    if (name === 'telegramId' && value.length >= 8) {
      const timeoutId = setTimeout(() => {
        verifyTelegramId(value)
      }, 500) // Debounce for 500ms

      return () => clearTimeout(timeoutId)
    }
  }

  const verifyTelegramId = async (telegramId: string) => {
    if (!telegramId || telegramId.length < 8) {
      setTelegramVerification(prev => ({
        ...prev,
        verified: false,
        telegramUser: null,
        error: ''
      }))
      return
    }

    setTelegramVerification(prev => ({ ...prev, loading: true, error: '' }))

    try {
      const telegramIdNum = parseInt(telegramId)
      const { supabase } = await import('../lib/supabase')

      // Check if Telegram ID exists in telegram_users table
      const { data: telegramUser, error: telegramError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramIdNum)
        .single()

      if (telegramError || !telegramUser) {
        setTelegramVerification({
          loading: false,
          error: 'Telegram ID not found. Please check your ID or register first.',
          verified: false,
          telegramUser: null,
          needsProfileCompletion: false
        })
        return
      }

      // Check if account has web access enabled by looking for linked users table record
      let needsProfileCompletion = true
      let linkedUser = null

      if (telegramUser.user_id) {
        // Check if there's a linked users table record
        const { data: userRecord, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', telegramUser.user_id)
          .single()

        if (!userError && userRecord) {
          needsProfileCompletion = false
          linkedUser = userRecord
        }
      }

      setTelegramVerification({
        loading: false,
        error: '',
        verified: true,
        telegramUser: {
          ...telegramUser,
          linkedUser // Add the linked user record for later use
        },
        needsProfileCompletion
      })

    } catch (error) {
      console.error('Telegram verification error:', error)
      setTelegramVerification({
        loading: false,
        error: 'Verification failed. Please try again.',
        verified: false,
        telegramUser: null,
        needsProfileCompletion: false
      })
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    if (loginMode === 'email') {
      // Email validation
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'Invalid email format'
      }

      // Password validation
      if (!formData.password) {
        newErrors.password = 'Password is required'
      }
    } else {
      // Telegram ID validation
      if (!formData.telegramId.trim()) {
        newErrors.telegramId = 'Telegram ID is required'
      } else if (!/^\d{8,12}$/.test(formData.telegramId)) {
        newErrors.telegramId = 'Telegram ID must be 8-12 digits'
      } else if (!telegramVerification.verified) {
        newErrors.telegramId = 'Please verify your Telegram ID first'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (loginMode === 'email') {
        // Email login
        const { user, error } = await signInWithEmailEnhanced(formData.email, formData.password)

        if (error) {
          setGeneralError(error.message)
        } else if (user) {
          console.log('✅ Email login successful:', user)
          onLoginSuccess(user)
        }
      } else {
        // Telegram login
        if (telegramVerification.needsProfileCompletion) {
          // Handle profile completion flow
          console.log('🔄 Telegram user needs profile completion')

          // Create a special user object for profile completion
          const incompleteUser = {
            id: `telegram_${telegramVerification.telegramUser.telegram_id}`,
            email: null,
            needsProfileCompletion: true,
            telegramUser: telegramVerification.telegramUser,
            user_metadata: {
              telegram_id: telegramVerification.telegramUser.telegram_id,
              first_name: telegramVerification.telegramUser.first_name,
              username: telegramVerification.telegramUser.username,
              profile_completion_required: true
            }
          }

          console.log('✅ Telegram authentication successful, redirecting to profile completion')
          onLoginSuccess(incompleteUser)
        } else {
          // Direct login for complete Telegram accounts
          const { signInWithTelegramProfile } = await import('../lib/supabase')

          // Use the Telegram profile login function
          const { user, error } = await signInWithTelegramProfile(telegramVerification.telegramUser)

          if (error) {
            setGeneralError('Telegram login failed. Please try email login or contact support.')
          } else {
            console.log('✅ Telegram login successful:', user)
            onLoginSuccess(user)
          }
        }
      }
    } catch (err) {
      console.error('Login error:', err)
      setGeneralError('Login failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-2">
            Sign In to Your Account
          </h3>
          <p className="text-gray-400">
            Access your Aureus Alliance Holdings dashboard
          </p>
        </div>

        {/* Login Mode Selection */}
        <div>
          <label className="block text-sm font-semibold text-gray-300 mb-4">
            How would you like to sign in? (Updated Version)
          </label>
          <div className="space-y-3">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="loginMode"
                value="email"
                checked={loginMode === 'email'}
                onChange={(e) => setLoginMode(e.target.value as 'email' | 'telegram')}
                className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
              />
              <span className="text-white">Email & Password</span>
            </label>
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="loginMode"
                value="telegram"
                checked={loginMode === 'telegram'}
                onChange={(e) => setLoginMode(e.target.value as 'email' | 'telegram')}
                className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
              />
              <span className="text-white">Telegram ID</span>
            </label>
          </div>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Login Fields */}
          {loginMode === 'email' && (
            <>
              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                    errors.email ? 'border-red-500/50' : 'border-gray-600/50'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-2 text-sm text-red-400">{errors.email}</p>
                )}
              </div>
            </>
          )}

          {/* Telegram Login Fields */}
          {loginMode === 'telegram' && (
            <div>
              <label htmlFor="telegramId" className="block text-sm font-semibold text-gray-300 mb-3">
                Telegram ID
              </label>
              <div className="space-y-2">
                <input
                  id="telegramId"
                  name="telegramId"
                  type="text"
                  value={formData.telegramId}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                    errors.telegramId ? 'border-red-500/50' : 'border-gray-600/50'
                  }`}
                  placeholder="Enter your Telegram ID (8-12 digits)"
                />
                {errors.telegramId && (
                  <p className="mt-2 text-sm text-red-400">{errors.telegramId}</p>
                )}
                <p className="text-xs text-gray-500">
                  To find your Telegram ID: Open @AureusAllianceBot → Main Menu → Connect to Web → Your ID will be displayed
                </p>
              </div>

              {/* Telegram Verification Status */}
              {telegramVerification.loading && (
                <div className="p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-blue-300">
                    <div className="w-4 h-4 border-2 border-blue-300/30 border-t-blue-300 rounded-full animate-spin"></div>
                    <span>Verifying Telegram ID...</span>
                  </div>
                </div>
              )}

              {telegramVerification.error && (
                <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-red-300">
                    <span>⚠️</span>
                    <span className="text-sm">{telegramVerification.error}</span>
                  </div>
                </div>
              )}

              {telegramVerification.verified && telegramVerification.telegramUser && (
                <div className="p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-green-300 mb-2">
                    <span className="text-lg">✅</span>
                    <span className="font-semibold">Telegram Account Found</span>
                  </div>
                  <p className="text-sm text-gray-300">
                    Found: <strong>{telegramVerification.telegramUser.first_name}</strong>
                    {telegramVerification.telegramUser.username && (
                      <span> (@{telegramVerification.telegramUser.username})</span>
                    )}
                  </p>
                  {telegramVerification.needsProfileCompletion && (
                    <p className="text-xs text-yellow-400 mt-1">
                      ⚠️ Profile completion required after login
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Password - Only for email login */}
          {loginMode === 'email' && (
            <div>
              <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                    errors.password ? 'border-red-500/50' : 'border-gray-600/50'
                  }`}
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                >
                  {showPassword ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-400">{errors.password}</p>
              )}
            </div>
          )}

          {/* General Error */}
          {generalError && (
            <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-xl text-red-300 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                {generalError}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full py-4 px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
            style={{ color: '#000000 !important' }}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2" style={{ color: '#000000 !important' }}>
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                <span style={{ color: '#000000 !important' }}>
                  {loginMode === 'telegram' ? 'Accessing Account...' : 'Signing In...'}
                </span>
              </div>
            ) : (
              <span style={{ color: '#000000 !important' }}>
                {loginMode === 'telegram' ? 'Access Account' : 'Sign In'}
              </span>
            )}
          </button>
        </form>

        {/* Forgot Password - Only for email login */}
        {loginMode === 'email' && (
          <div className="text-center">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-200"
              onClick={() => {
                // TODO: Implement forgot password functionality
                alert('Forgot password functionality will be implemented soon. Please contact support for now.')
              }}
            >
              Forgot your password?
            </button>
          </div>
        )}

        {/* Switch to Register */}
        <div className="text-center">
          <p className="text-gray-300 mb-4">Don't have an account yet?</p>
          <button
            onClick={onSwitchToRegister}
            className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            style={{ color: '#ffffff !important' }}
          >
            <span style={{ color: '#ffffff !important' }}>Create Account</span>
          </button>
        </div>



        {/* Security Notice */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Secure authentication powered by Supabase
          </p>
        </div>
      </div>
    </div>
  )
}
