import React, { useState } from 'react'
import { signInWithEmailEnhanced } from '../lib/supabase'

interface EmailLoginFormProps {
  onLoginSuccess: (user: any) => void
  onSwitchToRegister: () => void
}

export const EmailLoginForm: React.FC<EmailLoginFormProps> = ({ 
  onLoginSuccess, 
  onSwitchToRegister 
}) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [generalError, setGeneralError] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear field-specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
    
    // Clear general error when user starts typing
    if (generalError) {
      setGeneralError('')
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}
    
    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format'
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')
    
    if (!validateForm()) {
      return
    }
    
    setLoading(true)
    
    try {
      const { user, error } = await signInWithEmailEnhanced(formData.email, formData.password)
      
      if (error) {
        setGeneralError(error.message)
      } else if (user) {
        console.log('✅ Login successful:', user)
        onLoginSuccess(user)
      }
    } catch (err) {
      console.error('Login error:', err)
      setGeneralError('Login failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-2">
            Sign In to Your Account
          </h3>
          <p className="text-gray-400">
            Access your Aureus Alliance Holdings dashboard
          </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
              Email Address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.email ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="mt-2 text-sm text-red-400">{errors.email}</p>
            )}
          </div>

          {/* Password */}
          <div>
            <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.password ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="••••••••"
            />
            {errors.password && (
              <p className="mt-2 text-sm text-red-400">{errors.password}</p>
            )}
          </div>

          {/* General Error */}
          {generalError && (
            <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-xl text-red-300 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                {generalError}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full py-4 px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                Signing In...
              </div>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        {/* Forgot Password */}
        <div className="text-center">
          <button
            type="button"
            className="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-200"
            onClick={() => {
              // TODO: Implement forgot password functionality
              alert('Forgot password functionality will be implemented soon. Please contact support for now.')
            }}
          >
            Forgot your password?
          </button>
        </div>

        {/* Switch to Register */}
        <div className="text-center">
          <p className="text-gray-300 mb-4">Don't have an account yet?</p>
          <button
            onClick={onSwitchToRegister}
            className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            Create Account
          </button>
        </div>

        {/* Telegram Alternative */}
        <div className="border-t border-gray-700/50 pt-6">
          <div className="text-center">
            <p className="text-gray-400 mb-4">Or sign in with Telegram</p>
            <a
              href="https://t.me/AureusAllianceBot?start=web_login"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors duration-200"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.81-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.57c.43-.16.81.11.67.77z"/>
              </svg>
              <span>Sign in with Telegram</span>
            </a>
          </div>
        </div>

        {/* Security Notice */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Secure authentication powered by Supabase
          </p>
        </div>
      </div>
    </div>
  )
}
