const { Client } = require('pg');
require('dotenv').config();

console.log('🚀 Migrating Database for Campaign Tracking Support...');

// Database configuration using Supabase connection string
const dbConfig = {
  host: process.env.SUPABASE_DB_HOST,
  port: parseInt(process.env.SUPABASE_DB_PORT) || 5432,
  database: process.env.SUPABASE_DB_NAME,
  user: process.env.SUPABASE_DB_USER,
  password: process.env.SUPABASE_DB_PASSWORD,
  ssl: {
    rejectUnauthorized: false
  }
};

console.log('📋 Database configuration:');
console.log(`Host: ${dbConfig.host}`);
console.log(`Database: ${dbConfig.database}`);

async function migrateCampaignTracking() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('✅ Connected to Supabase PostgreSQL database!');
    
    // Run migrations in order
    await addCampaignSourceToReferrals(client);
    await addTelegramIdToUsers(client);
    await addCountryToUsers(client);
    await createReferralAnalyticsTable(client);
    await createReferralClicksTable(client);
    
    console.log('🎉 Campaign tracking migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await client.end();
  }
}

async function addCampaignSourceToReferrals(client) {
  console.log('🔄 Adding campaign_source column to referrals table...');
  
  try {
    // Check if column already exists
    const checkColumnSQL = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'referrals' AND column_name = 'campaign_source';
    `;
    
    const result = await client.query(checkColumnSQL);
    
    if (result.rows.length === 0) {
      // Column doesn't exist, add it
      const addColumnSQL = `
        ALTER TABLE referrals 
        ADD COLUMN campaign_source VARCHAR(100);
        
        CREATE INDEX IF NOT EXISTS idx_referrals_campaign_source ON referrals(campaign_source);
      `;
      
      await client.query(addColumnSQL);
      console.log('✅ Added campaign_source column to referrals table');
    } else {
      console.log('ℹ️ campaign_source column already exists in referrals table');
    }
  } catch (error) {
    console.error('❌ Error adding campaign_source column:', error);
    throw error;
  }
}

async function addTelegramIdToUsers(client) {
  console.log('🔄 Adding telegram_id column to users table...');
  
  try {
    // Check if column already exists
    const checkColumnSQL = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'telegram_id';
    `;
    
    const result = await client.query(checkColumnSQL);
    
    if (result.rows.length === 0) {
      // Column doesn't exist, add it
      const addColumnSQL = `
        ALTER TABLE users 
        ADD COLUMN telegram_id BIGINT UNIQUE;
        
        CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id);
      `;
      
      await client.query(addColumnSQL);
      console.log('✅ Added telegram_id column to users table');
    } else {
      console.log('ℹ️ telegram_id column already exists in users table');
    }
  } catch (error) {
    console.error('❌ Error adding telegram_id column:', error);
    throw error;
  }
}

async function addCountryToUsers(client) {
  console.log('🔄 Adding country_of_residence column to users table...');
  
  try {
    // Check if column already exists
    const checkColumnSQL = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'country_of_residence';
    `;
    
    const result = await client.query(checkColumnSQL);
    
    if (result.rows.length === 0) {
      // Column doesn't exist, add it
      const addColumnSQL = `
        ALTER TABLE users 
        ADD COLUMN country_of_residence VARCHAR(3) DEFAULT 'ZAF';
      `;
      
      await client.query(addColumnSQL);
      console.log('✅ Added country_of_residence column to users table');
    } else {
      console.log('ℹ️ country_of_residence column already exists in users table');
    }
  } catch (error) {
    console.error('❌ Error adding country_of_residence column:', error);
    throw error;
  }
}

async function createReferralAnalyticsTable(client) {
  console.log('🔄 Creating referral_analytics table...');

  const createReferralAnalyticsSQL = `
    CREATE TABLE IF NOT EXISTS referral_analytics (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      referrer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      campaign_name VARCHAR(100) NOT NULL,
      campaign_source VARCHAR(100),
      clicks INTEGER DEFAULT 0,
      registrations INTEGER DEFAULT 0,
      conversions INTEGER DEFAULT 0,
      conversion_rate DECIMAL(5,2) DEFAULT 0.00,
      total_revenue DECIMAL(15,2) DEFAULT 0.00,
      date_tracked DATE DEFAULT CURRENT_DATE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

      CONSTRAINT unique_daily_campaign UNIQUE(referrer_id, campaign_name, date_tracked)
    );

    CREATE INDEX IF NOT EXISTS idx_referral_analytics_referrer_id ON referral_analytics(referrer_id);
    CREATE INDEX IF NOT EXISTS idx_referral_analytics_campaign_name ON referral_analytics(campaign_name);
    CREATE INDEX IF NOT EXISTS idx_referral_analytics_campaign_source ON referral_analytics(campaign_source);
    CREATE INDEX IF NOT EXISTS idx_referral_analytics_date_tracked ON referral_analytics(date_tracked);
    CREATE INDEX IF NOT EXISTS idx_referral_analytics_conversion_rate ON referral_analytics(conversion_rate);
  `;

  await client.query(createReferralAnalyticsSQL);
  console.log('✅ Referral analytics table created');
}

async function createReferralClicksTable(client) {
  console.log('🔄 Creating referral_clicks table...');

  const createReferralClicksSQL = `
    CREATE TABLE IF NOT EXISTS referral_clicks (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      referrer_username VARCHAR(255) NOT NULL,
      campaign_source VARCHAR(100),
      ip_address INET,
      user_agent TEXT,
      referer_url TEXT,
      clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      converted BOOLEAN DEFAULT FALSE,
      converted_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_referral_clicks_referrer_username ON referral_clicks(referrer_username);
    CREATE INDEX IF NOT EXISTS idx_referral_clicks_campaign_source ON referral_clicks(campaign_source);
    CREATE INDEX IF NOT EXISTS idx_referral_clicks_clicked_at ON referral_clicks(clicked_at);
    CREATE INDEX IF NOT EXISTS idx_referral_clicks_converted ON referral_clicks(converted);
    CREATE INDEX IF NOT EXISTS idx_referral_clicks_ip_address ON referral_clicks(ip_address);
  `;

  await client.query(createReferralClicksSQL);
  console.log('✅ Referral clicks table created');
}

// Run migration if called directly
if (require.main === module) {
  migrateCampaignTracking()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

// Export the migration function
module.exports = {
  migrateCampaignTracking,
  dbConfig
};
