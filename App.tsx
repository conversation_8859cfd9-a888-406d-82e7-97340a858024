﻿
import React, { useState, useEffect, useCallback, useRef } from 'react';
import ErrorBoundary from './components/ErrorBoundary';
import { EnhancedGallery } from './components/gallery/EnhancedGallery';
import { AdminRouter } from './AdminRouter';
import { useSiteContentContext } from './contexts/SiteContentContext';
import { useGoldPrice } from './hooks/useGoldPrice';
import { PrivacyPolicy } from './components/PrivacyPolicy';
import { TermsAndConditions } from './components/TermsAndConditions';
import { LegalDisclaimer } from './components/LegalDisclaimer';
import { EmailLoginForm } from './components/EmailLoginForm';
import { EmailRegistrationForm } from './components/EmailRegistrationForm'
import { ProfileCompletionForm } from './components/ProfileCompletionForm';
import { UserDashboard } from './components/UserDashboard';


import { DEFAULT_VALUES, LAND_SIZE_OPTIONS, PLANT_CAPACITY_TPH, EFFECTIVE_HOURS_PER_DAY, OPERATING_DAYS_PER_YEAR, BULK_DENSITY_T_PER_M3, HA_PER_PLANT, TOTAL_SHARES, PROJECT_TOTAL_HA } from './constants';
import type { CalculatorInputs, CalculatedValues, ProjectionYear } from './types';

// --- ICONS ---
const ResetIcon: React.FC = () => (<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 10M20 20l-1.5-1.5A9 9 0 003.5 14" /></svg>);
const CheckIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${className}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>);
const ArrowRightIcon: React.FC = () => (<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M17 8l4 4m0 0l-4 4m4-4H3" /></svg>);
const TrophyIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M18.5 2h-13A2.5 2.5 0 003 4.5V9a2 2 0 002 2h2v2H5a2 2 0 00-2 2v2.5a2.5 2.5 0 002.5 2.5h13a2.5 2.5 0 002.5-2.5V15a2 2 0 00-2-2h-2v-2h2a2 2 0 002-2V4.5A2.5 2.5 0 0018.5 2zM12 13a1 1 0 01-1 1H7a1 1 0 01-1-1v-2h6v2zm6-2a1 1 0 01-1 1h-4v-2h5a1 1 0 011 1v1z"></path></svg>);
const CrownIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M5.18 4.6l-1.5 8.23 2.26.42 1.3-7.14 2.8 2.3-1.45 8.04h8.82l-1.45-8.04 2.8-2.3 1.3 7.14 2.26-.42-1.5-8.23L12 10.3 5.18 4.6zM18.5 20h-13a1 1 0 010-2h13a1 1 0 010 2z"></path></svg>);
const SecondPlaceIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm-1.5 13.5V14H8v-2h2.5V9.5h3V12H16v2h-2.5v1.5h-3z"></path></svg>);
const ThirdPlaceIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13.5v-1H9.5v-3h3v-1H13V9.67A1.5 1.5 0 0111.5 8.5a1.5 1.5 0 011.5-1.5v-1h-3v1a1.5 1.5 0 01-1.5 1.5 1.5 1.5 0 01-1.5-1.5v-1h-1v3h1.5v1H8v1.83A1.5 1.5 0 019.5 16a1.5 1.5 0 01-1.5 1.5v1h3v-1A1.5 1.5 0 019.5 15a1.5 1.5 0 011.5-1.5h.5v3h1.5z"></path></svg>);
const MedalIcon: React.FC<{className?: string}> = ({className}) => (<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 15a5 5 0 110-10 5 5 0 010 10z"></path><path d="M12 14a2 2 0 100-4 2 2 0 000 4z"></path></svg>);
const NetworkIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M19.41,12a1,1,0,0,0-.41-1.41L13.41,6.41a3,3,0,0,0-4.82,0L3,12l5.59,5.59a3,3,0,0,0,4.82,0L19,13.41A1,1,0,0,0,19.41,12ZM12,15.59A1.59,1.59,0,1,1,13.59,14,1.59,1.59,0,0,1,12,15.59Zm0-7.18A1.59,1.59,0,1,1,13.59,7,1.59,1.59,0,0,1,12,8.41ZM7,12A1.59,1.59,0,1,1,8.59,10.41,1.59,1.59,0,0,1,7,12Zm10,0a1.59,1.59,0,1,1,1.59-1.59A1.59,1.59,0,0,1,17,12Z"/></svg>;
const ChartPieIcon: React.FC<{ className?: string }> = ({ className }) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm1,17.93V13h8.93A8,8,0,0,1,13,19.93ZM13,11V4.07A8,8,0,0,1,20.93,11Z" /><path d="M11,17.93A8,8,0,0,1,4.07,13H11Z" /></svg>;
const HeartIcon: React.FC<{ className?: string }> = ({ className }) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5,2,5.42,4.42,3,7.5,3c1.74,0,3.41.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3,19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54Z" /></svg>;
const DocumentIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M14,2H6A2,2,0,0,0,4,4v16a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V8Zm2,16H8V4h5v5h5Z"/></svg>;
const VideoCameraIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M17,10.5V7a1,1,0,0,0-1-1H4A1,1,0,0,0,3,7v10a1,1,0,0,0,1,1h12a1,1,0,0,0,1-1v-3.5l4,4v-11Z"/></svg>;
const UsersIcon: React.FC<{className?: string}> = ({className}) => <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor"><path d="M16,13a4,4,0,1,0-4-4A4,4,0,0,0,16,13Zm4-6a2,2,0,1,0-2-2A2,2,0,0,0,20,7ZM4,13a4,4,0,1,0-4-4A4,4,0,0,0,4,13Zm4,6a2,2,0,1,0-2-2A2,2,0,0,0,8,19Z"/></svg>;

// --- HELPER & UI COMPONENTS ---
const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
  if (isNaN(num) || !isFinite(num)) return 'N/A';
  return new Intl.NumberFormat('en-US', options).format(num);
};

// --- ENHANCED ANIMATION WRAPPER ---
const AnimatedSectionWrapper: React.FC<{children: React.ReactNode, className?: string}> = ({ children, className = 'fade-in-section' }) => {
    const [isVisible, setVisible] = useState(false);
    const domRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const observer = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting) {
                setVisible(true);
                observer.unobserve(domRef.current!);
            }
        }, {
            threshold: 0.1,
            rootMargin: '50px 0px -50px 0px'
        });

        if (domRef.current) {
            observer.observe(domRef.current);
        }

        return () => {
            if (domRef.current) {
                observer.unobserve(domRef.current!);
            }
        };
    }, []);

    return (
        <div ref={domRef} className={`${className} ${isVisible ? 'is-visible' : ''}`}>
            {children}
        </div>
    );
};

// --- CALCULATOR COMPONENT ---
interface CalculatorProps {
    liveGoldPrice: number;
    goldPriceLoading: boolean;
    goldPriceError: string | null;
}

const Calculator: React.FC<CalculatorProps> = ({ liveGoldPrice, goldPriceLoading, goldPriceError }) => {
    let getContent: (section: string, key: string, defaultValue?: any) => any;

    try {
        const context = useSiteContentContext();
        getContent = context.getContent;
    } catch (error) {
        console.warn('Calculator: Site content context not available, using defaults');
        getContent = (section: string, key: string, defaultValue: any = '') => defaultValue;
    }
    const [inputs, setInputs] = useState<CalculatorInputs>(DEFAULT_VALUES);
    const [calculated, setCalculated] = useState<CalculatedValues>({} as CalculatedValues);
    const [projection, setProjection] = useState<ProjectionYear[]>([]);
    const [allocatedShares, setAllocatedShares] = useState(0);
    const [selectedYear, setSelectedYear] = useState(1);

    // Get dynamic values from database with safe parsing
    const calculatorTitle = getContent('calculator', 'title', 'Financial Calculator') || 'Financial Calculator';
    const calculatorSubtitle = getContent('calculator', 'subtitle', 'Experience the power of data-driven investment decisions with our interactive calculator') || 'Experience the power of data-driven investment decisions with our interactive calculator';
    const defaultLandSize = parseInt(getContent('calculator', 'default_land_size', '25') || '25') || 25;
    const defaultUserShares = parseInt(getContent('calculator', 'default_user_shares', '1000') || '1000') || 1000;
    const defaultGoldPrice = parseInt(getContent('calculator', 'gold_price_default', '100000') || '100000') || 100000;

    const resetToDefaults = useCallback(() => setInputs({
        ...DEFAULT_VALUES,
        landHa: defaultLandSize,
        userShares: defaultUserShares,
        goldPriceUsdPerKg: defaultGoldPrice
    }), [defaultLandSize, defaultUserShares, defaultGoldPrice]);

    // Update inputs when database values change
    useEffect(() => {
        setInputs(prev => ({
            ...prev,
            landHa: defaultLandSize,
            userShares: defaultUserShares,
            goldPriceUsdPerKg: defaultGoldPrice
        }));
    }, [defaultLandSize, defaultUserShares, defaultGoldPrice]);

    useEffect(() => {
        const { landHa, avgGravelThickness, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent, userShares, dividendPayoutPercent } = inputs;
        
        // Core Logic: Each wash plant processes exactly 25 hectares
        const numPlants = landHa / HA_PER_PLANT; // 25 hectares per plant
        const plantsPerProject = PROJECT_TOTAL_HA / HA_PER_PLANT;
        const sharesPerPlant = TOTAL_SHARES / plantsPerProject;
        setAllocatedShares(numPlants * sharesPerPlant);

        // Create 10-year expansion projection with proper land-to-plant scaling
        const newProjection: any[] = [];
        let totalPlants = 0;
        let totalRevenue = 0;
        let totalEbit = 0;
        let totalGold = 0;
        let totalDivPerShare = 0;
        let totalUserDiv = 0;

        for (let i = 0; i < 10; i++) {
            const year = i + 1;

            // Specific plant scaling as per requirements
            let yearNumPlants;
            if (year === 1) {
                yearNumPlants = 4; // Year 1: 4 plants
            } else if (year === 2) {
                yearNumPlants = 10; // Year 2: 10 plants
            } else {
                yearNumPlants = 10 + (year - 2) * 10; // Year 3+: 20, 30, 40, etc.
            }

            // Annual production calculations for this year
            const annualThroughputT = yearNumPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
            const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000;
            const annualRevenue = annualGoldKg * goldPriceUsdPerKg;
            const annualOperatingCost = annualRevenue * (opexPercent / 100);
            const annualEbit = annualRevenue - annualOperatingCost;

            // Dividend calculations for this year - based on production target
            // 4 plants = 1,848kg = $72.60 per 1000 shares annually
            const PRODUCTION_TARGET_4_PLANTS = 1848; // kg per year
            const DIVIDEND_PER_1000_SHARES = 72.60; // USD per year for 1000 shares
            const productionBasedDividendPerShare = DIVIDEND_PER_1000_SHARES / 1000; // $0.0726 per share
            const userAnnualDividend = productionBasedDividendPerShare * userShares;

            newProjection.push({
                year,
                ebit: annualEbit,
                dividendPerShare: productionBasedDividendPerShare,
                userDividend: userAnnualDividend,
                numPlants: yearNumPlants,
                annualGoldKg,
                annualRevenue,
            });

            // Accumulate totals for averages
            totalPlants += yearNumPlants;
            totalRevenue += annualRevenue;
            totalEbit += annualEbit;
            totalGold += annualGoldKg;
            totalDivPerShare += productionBasedDividendPerShare;
            totalUserDiv += userAnnualDividend;
        }
        setProjection(newProjection);


        // Direct calculations based on user's land size and shares
        const userNumPlants = landHa / HA_PER_PLANT; // Plants based on user's land
        const userAnnualThroughputT = userNumPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
        const userAnnualGoldKg = (userAnnualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000;
        const userAnnualRevenue = userAnnualGoldKg * goldPriceUsdPerKg;
        const userAnnualOperatingCost = userAnnualRevenue * (opexPercent / 100);
        const userAnnualEbit = userAnnualRevenue - userAnnualOperatingCost;

        // Calculate dividends based on actual production target
        // 4 plants produce 1,848kg annually at current gold price
        // This gives $72.60 dividend per 1000 shares (as per user specification)
        const PRODUCTION_TARGET_4_PLANTS = 1848; // kg per year
        const DIVIDEND_PER_1000_SHARES = 72.60; // USD per year for 1000 shares

        // Calculate dividend per share based on production target
        const productionBasedDividendPerShare = DIVIDEND_PER_1000_SHARES / 1000; // $0.0726 per share
        const userActualDividend = productionBasedDividendPerShare * userShares;

        setCalculated({
            numPlants: userNumPlants, // User's plants based on land size
            annualRevenue: userAnnualRevenue, // User's revenue based on land
            annualEbit: userAnnualEbit, // User's EBIT based on land
            annualGoldKg: userAnnualGoldKg, // User's gold production based on land
            dividendPerShare: productionBasedDividendPerShare, // Corrected dividend per share ($0.0726)
            userAnnualDividend: userActualDividend, // User's dividend based on production target
            volumeM3: landHa * 10000 * avgGravelThickness,
            tonnesInSitu: (landHa * 10000 * avgGravelThickness * BULK_DENSITY_T_PER_M3),
            containedGoldG: (landHa * 10000 * avgGravelThickness * BULK_DENSITY_T_PER_M3 * inSituGrade),
            annualThroughputT: userAnnualThroughputT,
            annualOperatingCost: userAnnualOperatingCost,
        });

    }, [inputs]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setInputs(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
    };
    
    const Card: React.FC<{title: string; children: React.ReactNode;}> = ({ title, children }) => (
        <div className="bg-black/20 rounded-lg p-3">
            <h3 className="text-sm font-bold text-gold mb-3">{title}</h3>
            <div className="space-y-2">{children}</div>
        </div>
    );

    const CalculatorInput: React.FC<{ label: string; name: keyof CalculatorInputs; value: number | string; onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void; unit?: string; type?: 'number' | 'select'; options?: number[]; step?: number; readOnly?: boolean; }> = ({ label, name, value, onChange, unit, type = 'number', options, step = 0.1, readOnly = false }) => (
        <div className="calculator-input-container">
            <label htmlFor={name} className="calculator-input-label">{label}</label>
            <div className="calculator-input-field">
            {type === 'select' ? (
                <select id={name} name={name} value={value} onChange={onChange} disabled={readOnly}>
                    {options?.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                </select>
            ) : (
                <input type="number" id={name} name={name} value={value} onChange={onChange} step={step} readOnly={readOnly} className={readOnly ? 'opacity-60 cursor-not-allowed' : ''}/>
            )}
            {unit && <span className="calculator-input-unit">{unit}</span>}
            </div>
        </div>
    );

    const CalculatedDisplay: React.FC<{label: string; value: string; unit?: string; isWarning?: boolean;}> = ({ label, value, unit, isWarning }) => (
        <div className={`flex items-center justify-between p-2 rounded ${isWarning ? 'bg-red-900/20' : 'bg-white/5'}`}>
            <span className={`text-sm ${isWarning ? 'text-red-300' : 'text-gray-200'}`}>{label}</span>
            <div className={`font-bold text-sm ${isWarning ? 'text-red-400' : 'text-white'}`}>
                <span>{value}</span>
                {unit && <span className="ml-1 text-amber-400">{unit}</span>}
            </div>
        </div>
    );

    const ProjectionTabs: React.FC<{projection: any[], selectedYear: number, onYearSelect: (year: number) => void}> = ({ projection, selectedYear, onYearSelect }) => {
        const selectedData = projection.find(p => p.year === selectedYear) || projection[0];

        return (
            <div className="projection-tabs-container">
                {/* Tab Navigation */}
                <div className="tab-navigation">
                    <div className="tab-scroll-container">
                        {projection.map(p => (
                            <button
                                key={p.year}
                                onClick={() => onYearSelect(p.year)}
                                className={`tab-button ${selectedYear === p.year ? 'tab-active' : 'tab-inactive'}`}
                            >
                                Year {p.year}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Tab Content */}
                <div className="tab-content">
                    <div className="year-header">
                        <h4 className="text-xl font-bold text-gold mb-4">Year {selectedYear} Projection</h4>
                    </div>

                    <div className="projection-metrics-grid">
                        <div className="metric-card">
                            <div className="metric-label">Plants in Operation</div>
                            <div className="metric-value">{formatNumber(selectedData?.numPlants || 0, {maximumFractionDigits: 1})}</div>
                            <div className="metric-unit">plants</div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-label">Annual EBIT</div>
                            <div className="metric-value text-green-400">{formatNumber(selectedData?.ebit || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                            <div className="metric-unit">earnings</div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-label">Gold Production</div>
                            <div className="metric-value text-amber-400">{formatNumber(selectedData?.annualGoldKg || 0, {maximumFractionDigits: 0})}</div>
                            <div className="metric-unit">kg/year</div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-label">Annual Revenue</div>
                            <div className="metric-value text-blue-400">{formatNumber(selectedData?.annualRevenue || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                            <div className="metric-unit">revenue</div>
                        </div>

                        <div className="metric-card highlight">
                            <div className="metric-label">Dividend per Share</div>
                            <div className="metric-value text-gold">{formatNumber(selectedData?.dividendPerShare || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 4 })}</div>
                            <div className="metric-unit">per share</div>
                        </div>

                        <div className="metric-card highlight">
                            <div className="metric-label">Your Annual Dividend</div>
                            <div className="metric-value text-gold font-bold">{formatNumber(selectedData?.userDividend || 0, { style: 'currency', currency: 'USD', minimumFractionDigits: 2 })}</div>
                            <div className="metric-unit">your return</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="card calculator-container">
            <header className="calculator-header">
                <div className="calculator-header-content">
                    <div className="calculator-title-section">
                        <h3 className="text-lg font-bold text-gold mb-1">Interactive {calculatorTitle}</h3>
                        <p className="text-sm text-gray-300">Model your share purchasing scenario to explore potential returns.</p>
                    </div>
                    <div className="calculator-reset-button">
                        <button onClick={resetToDefaults} className="btn btn-secondary flex items-center gap-2 text-sm">
                            <ResetIcon />
                            <span>Reset</span>
                        </button>
                    </div>
                </div>
            </header>

            <div className="calculator-layout-modern">
                {/* Modern Input Section */}
                <div className="calculator-inputs-modern">
                    <div className="inputs-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Investment Parameters</h3>
                        <p className="text-sm text-gray-400 mb-6">Configure your investment scenario and project assumptions</p>
                    </div>

                    <div className="modern-inputs-grid">
                        {/* Editable Fields */}
                        <div className="input-group editable">
                            <label className="input-label">Land Size</label>
                            <select
                                name="landHa"
                                value={inputs.landHa}
                                onChange={handleInputChange}
                                className="modern-input"
                            >
                                {LAND_SIZE_OPTIONS.map(size => (
                                    <option key={size} value={size}>{size} ha</option>
                                ))}
                            </select>
                            <div className="input-info">
                                <span className="info-badge">Corresponds to {formatNumber(allocatedShares)} shares</span>
                            </div>
                        </div>

                        <div className="input-group editable">
                            <label className="input-label">Your Shares</label>
                            <input
                                type="number"
                                name="userShares"
                                value={inputs.userShares}
                                onChange={handleInputChange}
                                step={100}
                                className="modern-input"
                            />
                            <div className="input-info">
                                <span className="info-text">Total project shares: {formatNumber(TOTAL_SHARES)}</span>
                            </div>
                        </div>

                        <div className="input-group editable gold-price-group">
                            <label className="input-label">Gold Price</label>
                            <input
                                type="number"
                                name="goldPriceUsdPerKg"
                                value={inputs.goldPriceUsdPerKg}
                                onChange={handleInputChange}
                                step={1000}
                                className="modern-input"
                            />
                            <div className="input-info">
                                <div className="gold-price-controls">
                                    <button
                                        type="button"
                                        onClick={() => setInputs(prev => ({ ...prev, goldPriceUsdPerKg: liveGoldPrice }))}
                                        disabled={goldPriceLoading}
                                        className="live-price-btn"
                                    >
                                        {goldPriceLoading ? 'Loading...' : 'Use Live Price'}
                                    </button>
                                    <span className="live-price-display">
                                        Live: ${liveGoldPrice.toLocaleString()}/kg
                                    </span>
                                </div>
                                {goldPriceError && (
                                    <span className="error-text">({goldPriceError})</span>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Modern Results Section */}
                <div className="calculator-results-modern">
                    <div className="results-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Financial Projections</h3>
                        <p className="text-sm text-gray-400 mb-6">Based on your investment parameters</p>
                    </div>

                    <div className="results-grid">
                        <div className="result-card operational">
                            <div className="result-icon">🏭</div>
                            <div className="result-content">
                                <div className="result-label">Plants for Your Land</div>
                                <div className="result-value">{formatNumber(calculated.numPlants, {maximumFractionDigits: 1})}</div>
                                <div className="result-unit">plants</div>
                            </div>
                        </div>

                        <div className="result-card revenue">
                            <div className="result-icon">💰</div>
                            <div className="result-content">
                                <div className="result-label">Land Revenue Potential</div>
                                <div className="result-value">{formatNumber(calculated.annualRevenue, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                                <div className="result-unit">per year</div>
                            </div>
                        </div>

                        <div className="result-card ebit">
                            <div className="result-icon">📈</div>
                            <div className="result-content">
                                <div className="result-label">Land EBIT Potential</div>
                                <div className="result-value">{formatNumber(calculated.annualEbit, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}</div>
                                <div className="result-unit">earnings</div>
                            </div>
                        </div>

                        <div className="result-card gold">
                            <div className="result-icon">🥇</div>
                            <div className="result-content">
                                <div className="result-label">Land Gold Production</div>
                                <div className="result-value">{formatNumber(calculated.annualGoldKg, { maximumFractionDigits: 2 })}</div>
                                <div className="result-unit">kg/year</div>
                            </div>
                        </div>

                        <div className="result-card dividend highlight">
                            <div className="result-icon">💎</div>
                            <div className="result-content">
                                <div className="result-label">Your Annual Dividend</div>
                                <div className="result-value">{formatNumber(calculated.userAnnualDividend, { style: 'currency', currency: 'USD', minimumFractionDigits: 2 })}</div>
                                <div className="result-unit">your return</div>
                            </div>
                        </div>

                        <div className="result-card dividend-share highlight">
                            <div className="result-icon">📊</div>
                            <div className="result-content">
                                <div className="result-label">Dividend per Share</div>
                                <div className="result-value">{formatNumber(calculated.dividendPerShare, { style: 'currency', currency: 'USD', minimumFractionDigits: 4 })}</div>
                                <div className="result-unit">per share</div>
                            </div>
                        </div>
                    </div>

                    {/* Project Assumptions Subsection */}
                    <div className="project-assumptions-section">
                        <div className="assumptions-header">
                            <h4 className="text-lg font-semibold text-gold mb-3">Project Assumptions</h4>
                            <p className="text-xs text-gray-400 mb-4">Technical parameters used in calculations</p>
                        </div>

                        <div className="assumptions-grid">
                            <div className="assumption-item">
                                <span className="assumption-label">Gravel Thickness</span>
                                <span className="assumption-value">{inputs.avgGravelThickness} meters</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">In-situ Grade</span>
                                <span className="assumption-value">{inputs.inSituGrade} g/m³</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">Recovery Factor</span>
                                <span className="assumption-value">{inputs.recoveryFactor}% efficiency</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">Operating Cost</span>
                                <span className="assumption-value">{inputs.opexPercent}% of revenue</span>
                            </div>

                            <div className="assumption-item">
                                <span className="assumption-label">Dividend Payout</span>
                                <span className="assumption-value">{inputs.dividendPayoutPercent}% of EBIT</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Year-by-Year Projection */}
                <div className="projection-section-modern">
                    <div className="projection-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Year-by-Year Expansion</h3>
                        <p className="text-sm text-gray-400 mb-6">Detailed projections for each year of operation</p>
                    </div>

                    <ProjectionTabs
                        projection={projection}
                        selectedYear={selectedYear}
                        onYearSelect={setSelectedYear}
                    />
                </div>
            </div>
        </div>
    );
};

// --- NEW SITE COMPONENTS ---
const NavLink: React.FC<{href: string; children: React.ReactNode}> = ({href, children}) => (
    <a href={href} className="relative text-gray-300 hover:text-white transition-all duration-300 group">
        {children}
        <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-amber-400 to-amber-600 group-hover:w-full transition-all duration-300"></span>
    </a>
);

const Header: React.FC<{setCurrentSection: (section: string) => void}> = ({setCurrentSection}) => {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    return (
        <header className="header-container">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
            <div className="container">
                <nav className="header-nav">
                    {/* Desktop single-row layout */}
                    <a href="#" className="header-logo-image float-animation desktop-logo">
                        <img
                            src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                            alt="Aureus Alliance Holdings - Professional Gold Mining Investment Company Logo"
                            className="header-logo-img"
                        />
                    </a>

                    <div className="desktop-nav desktop-nav-single">
                        <NavLink href="#about">About</NavLink>
                        <NavLink href="#highlights">Highlights</NavLink>
                        <NavLink href="#commission">Commission</NavLink>
                        <NavLink href="#gold-diggers-club">Gold Diggers Club</NavLink>
                        <NavLink href="#calculator">Calculator</NavLink>
                        <NavLink href="#project">Project</NavLink>
                    </div>

                    <div className="desktop-actions desktop-actions-single">
                        <button onClick={() => setCurrentSection('login')} className="btn btn-outline">
                            Login
                        </button>
                        <button onClick={() => setCurrentSection('register')} className="btn btn-secondary">
                            Register
                        </button>
                        <a href="https://t.me/AureusAllianceBot" target="_blank" rel="noopener noreferrer" className="btn btn-primary">
                            Purchase Shares
                        </a>
                    </div>

                    {/* Two-row layout for tablets */}
                    <div className="header-nav-top tablet-layout">
                        <a href="#" className="header-logo-image float-animation">
                            <img
                                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                                alt="Aureus Alliance Holdings - Professional Gold Mining Investment Company Logo"
                                className="header-logo-img"
                            />
                        </a>

                        <div className="desktop-actions">
                            <button onClick={() => setCurrentSection('login')} className="btn btn-outline">
                                Login
                            </button>
                            <button onClick={() => setCurrentSection('register')} className="btn btn-secondary">
                                Register
                            </button>
                            <a href="https://t.me/AureusAllianceBot" target="_blank" rel="noopener noreferrer" className="btn btn-primary">
                                Purchase Shares
                            </a>
                        </div>
                    </div>

                    <div className="header-nav-bottom tablet-layout">
                        <div className="desktop-nav">
                            <NavLink href="#about">About</NavLink>
                            <NavLink href="#highlights">Highlights</NavLink>
                            <NavLink href="#commission">Commission</NavLink>
                            <NavLink href="#gold-diggers-club">Gold Diggers Club</NavLink>
                            <NavLink href="#calculator">Calculator</NavLink>
                            <NavLink href="#project">Project</NavLink>
                        </div>
                    </div>

                    {/* Mobile Menu Button */}
                    <button
                        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                        className="mobile-menu-button"
                    >
                        <div className="hamburger-icon">
                            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
                            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
                            <span className={`hamburger-line ${mobileMenuOpen ? 'open' : ''}`}></span>
                        </div>
                    </button>
                </nav>
            </div>

            {/* Mobile Menu Overlay */}
            <div className={`mobile-menu-overlay ${mobileMenuOpen ? 'open' : ''}`} onClick={() => setMobileMenuOpen(false)}></div>

            {/* Mobile Menu Panel */}
            <div className={`mobile-menu-panel ${mobileMenuOpen ? 'open' : ''}`}>
                <div className="mobile-menu-content">
                    <nav className="mobile-menu-nav">
                        <NavLink href="#about" onClick={() => setMobileMenuOpen(false)}>About</NavLink>
                        <NavLink href="#highlights" onClick={() => setMobileMenuOpen(false)}>Highlights</NavLink>
                        <NavLink href="#commission" onClick={() => setMobileMenuOpen(false)}>Commission</NavLink>
                        <NavLink href="#gold-diggers-club" onClick={() => setMobileMenuOpen(false)}>Gold Diggers Club</NavLink>
                        <NavLink href="#calculator" onClick={() => setMobileMenuOpen(false)}>Calculator</NavLink>
                        <NavLink href="#project" onClick={() => setMobileMenuOpen(false)}>Project</NavLink>
                    </nav>

                    <div className="mobile-menu-actions">
                        <button onClick={() => {setCurrentSection('login'); setMobileMenuOpen(false);}} className="btn btn-outline btn-block" style={{marginBottom: '0.5rem'}}>
                            Login
                        </button>
                        <button onClick={() => {setCurrentSection('register'); setMobileMenuOpen(false);}} className="btn btn-secondary btn-block" style={{marginBottom: '0.5rem'}}>
                            Register
                        </button>
                        <a href="https://t.me/AureusAllianceBot" target="_blank" rel="noopener noreferrer" className="btn btn-primary btn-block">
                            Purchase Shares
                        </a>
                    </div>
                </div>
            </div>
        </header>
    );
};

const HeroSection: React.FC = () => (
    <section className="hero-section" style={{backgroundImage: `url('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/hero-background.jpg')`}}>
        <div className="absolute inset-0 bg-black/70"></div>

        <div className="container relative z-10">
            <div className="hero-content-wrapper">
                {/* Left Side - Main Content */}
                <div className="hero-left-content">
                    <h1 className="heading-hero mb-sm">
                        <span className="text-gold">Secure Your Wealth</span>
                        <span className="text-white"> with Real Gold</span>
                    </h1>

                    <p className="text-lg mb-md max-w-xl">
                        Professional gold mining investment with blockchain-backed ownership and transparent profit sharing.
                    </p>

                    <div className="hero-buttons">
                        <a href="#about" className="btn btn-primary">Explore Investment</a>
                        <a href="#calculator" className="btn btn-secondary">Calculate Returns</a>
                    </div>

                    <div className="hero-features">
                        <div className="flex items-center gap-xs">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-sm">Verified Operations</span>
                        </div>
                        <div className="flex items-center gap-xs">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span className="text-sm">Blockchain Secured</span>
                        </div>
                        <div className="flex items-center gap-xs">
                            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            <span className="text-sm">Sustainable Mining</span>
                        </div>
                    </div>

                    {/* Key Statistics */}
                    <div className="hero-stats-grid">
                        <div className="card text-center">
                            <div className="text-2xl font-bold text-gold mb-xs">$5</div>
                            <div className="text-sm mb-xs">Per Share</div>
                            <div className="text-sm">Presale Price</div>
                        </div>

                        <div className="card text-center">
                            <div className="text-2xl font-bold text-green-400 mb-xs">$134.51</div>
                            <div className="text-sm mb-xs">Annual Dividend</div>
                            <div className="text-sm">Projected</div>
                        </div>

                        <div className="card text-center">
                            <div className="text-2xl font-bold mb-xs">250 ha</div>
                            <div className="text-sm mb-xs">Concession</div>
                            <div className="text-sm">Verified</div>
                        </div>
                    </div>
                </div>

                {/* Right Side - Large Aureus Logo */}
                <div className="hero-right-content">
                    <img
                        src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                        alt="Aureus Alliance Holdings - Sustainable Gold Mining Operations and Investment Platform"
                        className="hero-main-logo"
                    />
                </div>
            </div>
        </div>
    </section>
);

const Section: React.FC<{id?: string, title: string, subtitle: string, children: React.ReactNode, className?: string}> = ({id, title, subtitle, children, className=""}) => (
    <section id={id} className={`section relative ${className}`}>
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900/20 to-black/40"></div>

        <div className="container relative z-10">
            <div className="text-center mb-md">
                <h2 className="heading-xl text-gold mb-xs">
                    {title}
                </h2>
                <p className="text-lg max-w-2xl mx-auto">
                    {subtitle}
                </p>
            </div>
            {children}
        </div>
    </section>
);

const CtaSection: React.FC = () => (
    <div className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-900/10 via-transparent to-blue-900/10"></div>
        <div className="absolute inset-0 cyber-grid opacity-20"></div>

        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div className="aureus-container max-w-5xl mx-auto">
                <div className="aureus-container-inner">
                    <div className="flex items-center justify-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-amber-600 rounded-xl flex items-center justify-center">
                            <span className="text-black font-bold text-xl"></span>
                        </div>
                        <h3 className="text-3xl md:text-4xl font-black text-gradient-gold">Ready to Become a Shareholder?</h3>
                    </div>

                    <p className="text-lg md:text-xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed">
                        Purchase shares directly and securely through our dedicated Telegram bot. Get started on your journey to owning a piece of this revolutionary project.
                    </p>

                    <div className="flex flex-col lg:flex-row gap-8 justify-center items-center">
                        <a href="https://t.me/AureusAllianceBot" target="_blank" rel="noopener noreferrer"
                           className="btn-primary text-lg px-8 py-4 inline-flex items-center gap-3 pulse-glow transform hover:scale-105 transition-all duration-300">
                            Purchase Shares on Telegram <ArrowRightIcon />
                        </a>

                        <div className="glass-card-strong p-6 rounded-2xl">
                            <div className="flex items-center justify-center gap-6 text-sm">
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                    <span className="text-white font-semibold">Secure</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
                                    <span className="text-white font-semibold">Instant</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 bg-amber-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
                                    <span className="text-white font-semibold">Verified</span>
                                </div>
                            </div>
                            <div className="text-gray-400 text-sm mt-2">NFT-backed ownership certificates</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
);

const AboutAureusSection: React.FC = () => {
    const timeline = [
        { date: "Now", title: "Pre-Seed Angel Round", desc: "Limited to $1,000,000 total for this round." },
        { date: "July", title: "NFT Equity Share Presale Launch", desc: "Public offering at higher valuation." },
        { date: "Q3 2025", title: "Gaming Platform Alpha", desc: "Early access for shareholders and NFT holders." }
    ];
    const advantages = [
        { title: "Gold-Backed Stability", desc: "Unlike purely speculative digital assets, Aureus is backed by physical gold mining operations with real-world value and cash flow." },
        { title: "Multi-Stream Revenue", desc: "Our business model combines income from gold production, NFT sales, gaming microtransactions, and marketplace fees." },
        { title: "Exponential Growth Potential", desc: "The integration of traditional mining with cutting-edge digital assets creates unique synergies and market positioning." }
    ];

    return (
        <section id="about" className="relative py-24 lg:py-32 overflow-hidden">
            {/* Enhanced Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"></div>
            <div className="absolute inset-0 cyber-grid opacity-20"></div>

            {/* Floating Background Elements */}
            <div className="absolute top-1/4 -left-32 w-96 h-96 bg-amber-500/10 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/4 -right-32 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>

            <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                {/* Section Header */}
                <div className="text-center mb-12">
                    <h2 className="text-4xl md:text-5xl lg:text-6xl font-black text-gradient-gold mb-4 tracking-tight">
                        About Aureus Alliance
                    </h2>
                    <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                        A revolutionary approach to gold mining investment through blockchain technology and sustainable practices.
                    </p>
                    <div className="flex justify-center mt-4">
                        <div className="w-24 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent rounded-full"></div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="grid grid-3 gap-md">
                    {/* Company Overview */}
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">Our Mission</h3>
                        <div className="space-y-2">
                            <p className="text-sm">Aureus Alliance Holdings bridges traditional gold mining with modern blockchain technology.</p>
                            <p className="text-sm">Built on verified mining concessions with proven gold reserves, providing stable returns.</p>
                            <p className="text-sm">Direct participation in mining profits through NFT-backed ownership shares.</p>
                        </div>
                    </div>

                    {/* Key Advantages */}
                    <div className="card">
                        <h4 className="heading-md text-gold mb-sm">Key Advantages</h4>
                        <div className="space-y-2">
                            {advantages.slice(0, 3).map((adv, index) => (
                                <div key={index} className="p-xs border border-white/10 rounded">
                                    <h5 className="text-gold font-semibold text-sm mb-xs">{adv.title}</h5>
                                    <p className="text-sm">{adv.desc}</p>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Development Timeline */}
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">Development Roadmap</h3>
                        <div className="space-y-2">
                            {timeline.slice(0, 4).map((item, index) => (
                                <div key={index} className="flex gap-sm">
                                    <div className="w-6 h-6 bg-gold rounded-full flex items-center justify-center text-black font-bold text-sm flex-shrink-0">
                                        {index + 1}
                                    </div>
                                    <div className="flex-1">
                                        <div className="text-gold text-sm font-semibold">{item.date}</div>
                                        <h4 className="font-semibold text-sm">{item.title}</h4>
                                        <p className="text-sm">{item.desc}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

const DonutChart: React.FC<{ data: { name: string; value: number; color: string }[] }> = ({ data }) => {
    const [hoveredSlice, setHoveredSlice] = useState<string | null>(null);
    const size = 200;
    const strokeWidth = 25;
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    let accumulatedPercentage = 0;

    return (
        <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="relative" style={{ width: size, height: size }}>
                <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`} className="-rotate-90">
                    {data.map((slice, index) => {
                        const percentage = slice.value;
                        const strokeDashoffset = circumference - (accumulatedPercentage / 100 * circumference);
                        const strokeDasharray = `${percentage / 100 * circumference} ${circumference}`;
                        accumulatedPercentage += percentage;
                        return (
                            <circle
                                key={index}
                                cx={size / 2}
                                cy={size / 2}
                                r={radius}
                                fill="transparent"
                                stroke={slice.color}
                                strokeWidth={strokeWidth}
                                strokeDasharray={strokeDasharray}
                                strokeDashoffset={strokeDashoffset}
                                className={`transition-all duration-300 ${hoveredSlice === slice.name ? 'opacity-100' : 'opacity-70'}`}
                                onMouseEnter={() => setHoveredSlice(slice.name)}
                                onMouseLeave={() => setHoveredSlice(null)}
                            />
                        );
                    })}
                </svg>
                 <div className="absolute inset-0 flex items-center justify-center text-center">
                    <div>
                        <p className="text-3xl font-bold gold-gradient-text">{hoveredSlice ? `${data.find(d=>d.name === hoveredSlice)?.value}%` : '100%'}</p>
                        <p className="text-sm text-gray-400 uppercase tracking-wider">{hoveredSlice || 'Total'}</p>
                    </div>
                </div>
            </div>
            <ul className="space-y-2">
                {data.map((slice) => (
                    <li key={slice.name} className="flex items-center gap-3 text-sm" onMouseEnter={() => setHoveredSlice(slice.name)} onMouseLeave={() => setHoveredSlice(null)}>
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: slice.color }}></div>
                        <span className={`transition-colors ${hoveredSlice === slice.name ? 'text-white font-bold' : 'text-gray-400'}`}>
                            {slice.name}: <span className="font-semibold">{slice.value}%</span>
                        </span>
                    </li>
                ))}
            </ul>
        </div>
    );
};

const KeyHighlightsSection: React.FC = () => {
    const whyAureus = [
        "Fully verified gold mine operations",
        "Real gold production, not just token promises",
        "NFT-based verified ownership",
        "Sustainable profit model backed by physical gold",
    ];
    const transparencyPoints = [
        { icon: <VideoCameraIcon className="w-6 h-6 text-amber-500"/>, text: "Real mine site footage" },
        { icon: <DocumentIcon className="w-6 h-6 text-amber-500"/>, text: "Full documentation" },
        { icon: <UsersIcon className="w-6 h-6 text-amber-500"/>, text: "Verifiable by the community" },
    ];
    const revenueAllocationData = [
        { name: 'Mining operations & infra', value: 40, color: '#D9A44D' },
        { name: 'SUN platform development', value: 30, color: '#A97E33' },
        { name: 'Direct sales commission', value: 15, color: '#8C6827' },
        { name: 'Gold Diggers Club rewards', value: 15, color: '#6F521B' },
        { name: 'Global charity support', value: 0, color: '#523C0F' },
    ];

    return (
        <section id="highlights" className="relative py-24 lg:py-32 overflow-hidden">
            {/* Enhanced Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900"></div>
            <div className="absolute inset-0 cyber-grid opacity-30"></div>

            {/* Floating Background Elements */}
            <div className="absolute top-1/3 -left-40 w-80 h-80 bg-amber-500/5 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/3 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>

            <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                {/* Section Header */}
                <div className="text-center mb-12">
                    <h2 className="text-4xl md:text-5xl lg:text-6xl font-black text-gradient-gold mb-4 tracking-tight">
                        Key Highlights
                    </h2>
                    <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                        Core strengths and financial incentives that make Aureus a unique opportunity.
                    </p>
                    <div className="flex justify-center mt-4">
                        <div className="w-24 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent rounded-full"></div>
                    </div>
                </div>

                {/* Main Content Layout */}
                <div className="grid grid-4 gap-md">
                    {/* Presale Offer */}
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">NFT Share Presale</h3>
                        <p className="text-sm mb-sm">Real gold mining meets blockchain technology.</p>
                        <div className="mb-sm">
                            <span className="text-2xl font-bold text-gold">$5</span>
                            <span className="text-sm ml-1">/ share</span>
                        </div>
                        <div className="p-sm bg-white/5 rounded">
                            <p className="text-sm mb-xs">Only <span className="font-bold">200,000 shares</span> available!</p>
                            <p className="text-sm">20 phases with increasing value.</p>
                        </div>
                    </div>

                    {/* Why Aureus */}
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">Why Choose Aureus?</h3>
                        <div className="space-y-2">
                            {whyAureus.slice(0, 3).map(point => (
                                <div key={point} className="flex items-start gap-xs">
                                    <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                    <span className="text-sm">{point}</span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Transparency */}
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">Full Transparency</h3>
                        <div className="space-y-2">
                            {whyAureus.slice(3).map(point => (
                                <div key={point} className="flex items-start gap-xs">
                                    <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                    <span className="text-sm">{point}</span>
                                </div>
                            ))}
                            {transparencyPoints.slice(0, 2).map(point => (
                                <div key={point.text} className="flex items-center gap-xs text-sm">
                                    {point.icon}
                                    <span>{point.text}</span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Investment Benefits */}
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">Investment Benefits</h3>
                        <div className="space-y-2">
                            <div className="flex items-start gap-xs">
                                <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                <span className="text-sm">Verified mining operations</span>
                            </div>
                            <div className="flex items-start gap-xs">
                                <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                <span className="text-sm">Blockchain-secured ownership</span>
                            </div>
                            <div className="flex items-start gap-xs">
                                <CheckIcon className="text-green-400 flex-shrink-0 w-4 h-4 mt-0.5" />
                                <span className="text-sm">Transparent profit sharing</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
};

const HumanitarianProjectsContent: React.FC = () => {
    const humanitarianImageUrl = 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/sustainable-villages.jpg';

    const villagePoints = [
      "A self-sustaining, solar-powered smart village",
      "Focus: housing, education, nutrition, farming, clean water",
      "Located near Kadoma gold operations in Zimbabwe",
      "Naming rights formalized via Aureus Alliance Holdings",
    ];

    const clinicPoints = [
      "A series of mobile and modular health clinics",
      "Focus: maternal health, vaccination, rural care",
      "First clinics to open in Kadoma Zimbabwe",
      "Powered by share sales, named via Aureus Alliance Holdings",
    ];
    
    const TickIcon: React.FC<{className?: string}> = ({className}) => (<svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 13l4 4L19 7" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/></svg>);

    const ListPoint: React.FC<{children: React.ReactNode}> = ({children}) => (
        <li className="flex items-start gap-3">
            <div className="mt-1.5 w-4 h-4 flex-shrink-0 rounded-full bg-white/80 flex items-center justify-center">
                <TickIcon className="w-2.5 h-2.5 text-amber-500" />
            </div>
            <span className="text-white">{children}</span>
        </li>
    );

    return (
      <div className="mt-12">
        <div className="grid lg:grid-cols-2 gap-8 xl:gap-16">
          <div className="space-y-6 flex flex-col">
              <header>
                  <h2 className="text-4xl md:text-5xl font-black uppercase tracking-wider text-white">Sustainable Villages</h2>
                  <h3 className="text-4xl md:text-5xl font-black uppercase -mt-2 gold-text">Health Trust Clinics</h3>
              </header>
              <img src={humanitarianImageUrl} alt="Aureus Alliance Holdings Sustainable Villages and Health Trust Clinics - Community Development Projects in Zimbabwe" className="rounded-lg shadow-2xl w-full" />
          </div>
          <div className="flex flex-col justify-start pt-4 text-white">
              <div className="space-y-8">
                  <p className="text-lg font-bold uppercase tracking-wide">
                  </p>
                  <div className="space-y-4">
                      <h4 className="text-2xl font-bold gold-text">SUSTAINABLE VILLAGE:</h4>
                      <ul className="space-y-2.5">
                          {villagePoints.map(point => <ListPoint key={point}>{point}</ListPoint>)}
                      </ul>
                  </div>
                  <div className="space-y-4">
                      <h4 className="text-2xl font-bold gold-text">HEALTH TRUST CLINICS:</h4>
                      <ul className="space-y-2.5">
                          {clinicPoints.map(point => <ListPoint key={point}>{point}</ListPoint>)}
                      </ul>
                  </div>
              </div>
          </div>
        </div>
        <footer className="text-center mt-16 text-white">
          <p className="text-2xl font-black uppercase tracking-wide max-w-5xl mx-auto">
              These projects transform ROI into visible humanitarian monuments — immortalizing leadership through community revival.</p>
        </footer>
      </div>
    );
};

const CharitySection: React.FC = () => (
    <section id="charity" className="section">
        <div className="container">
            <div className="text-center mb-md">
                <h2 className="heading-xl text-gold mb-xs">Charity & Community Impact</h2>
                <p className="text-lg max-w-2xl mx-auto">Aureus Alliance is committed to donating over $28,000,000 to charities worldwide. A core tenant of our philosophy is creating legacy projects that deliver lasting, positive change far beyond financial returns.</p>
            </div>
            <HumanitarianProjectsContent />
        </div>
    </section>
);


const NetworkerCommissionSection: React.FC = () => (
    <section id="commission" className="section">
        <div className="container">
            <div className="commission-header">
                <h2 className="commission-title">Networker Commission Structure</h2>
                <p className="commission-subtitle">Earn dual rewards in USDT & NFT Shares through our transparent direct sales commission structure.</p>
            </div>

            <div className="commission-structure">
                <div className="commission-card direct-sales">
                    <div className="commission-card-header">
                        <div className="commission-icon">💼</div>
                        <h3 className="commission-card-title">Direct Sales</h3>
                    </div>
                    <div className="commission-details">
                        <div className="commission-item">
                            <div className="commission-label">
                                <span className="commission-currency">💰</span>
                                USDT Commission
                            </div>
                            <div className="commission-value">15%</div>
                        </div>
                        <div className="commission-item">
                            <div className="commission-label">
                                <span className="commission-currency">🎁</span>
                                NFT Bonus
                            </div>
                            <div className="commission-value">15%</div>
                        </div>
                    </div>
                </div>

                <div className="commission-card example-card">
                    <div className="commission-card-header">
                        <div className="commission-icon">📊</div>
                        <h3 className="commission-card-title">Example: $1,000 Sale</h3>
                    </div>
                    <div className="commission-example">
                        <div className="example-reward primary">
                            <div className="reward-amount">$150</div>
                            <div className="reward-type">USDT</div>
                        </div>
                        <div className="example-plus">+</div>
                        <div className="example-reward secondary">
                            <div className="reward-amount">30</div>
                            <div className="reward-type">NFT Shares</div>
                        </div>
                    </div>
                </div>

                <div className="commission-card total-pool">
                    <div className="commission-card-header">
                        <div className="commission-icon">🏆</div>
                        <h3 className="commission-card-title">Total Pool</h3>
                    </div>
                    <div className="pool-details">
                        <div className="pool-amount">$450,000</div>
                        <div className="pool-description">45% of Presale Funds</div>
                        <div className="pool-visual">
                            <div className="pool-bar">
                                <div className="pool-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
);

const ProjectSection: React.FC = () => {
    const points = [ "A 2020 geotechnical report confirms the site's viability.", "Managed in-house with an experienced African operator.", "High-efficiency plant from a firm with 32+ years of expertise.", "Secure sales process directly to Fidelity Gold Refinery." ];
    return (
        <section id="project" className="section">
            <div className="container">
                <div className="text-center mb-md">
                    <h2 className="heading-xl text-gold mb-xs">The Land & Project Status</h2>
                    <p className="text-lg max-w-2xl mx-auto">A secured 250-hectare mining block confirmed for viability and primed for scalable, high-yield gold mining.</p>
                </div>

                <div className="grid grid-2 gap-md items-center">
                    <div className="card">
                        <h3 className="heading-md text-gold mb-sm">Strategic Viability</h3>
                        <p className="text-sm mb-sm">The licensed area is in Kadoma, a region renowned for rich gold placer deposits. Its connectivity ensures smooth logistics and uninterrupted operations.</p>
                        <ul className="space-y-xs">
                            {points.map((point, i) => (
                                <li key={i} className="flex items-start gap-xs text-sm">
                                    <CheckIcon className="text-amber-500 mt-0.5 flex-shrink-0 w-4 h-4" />
                                    <span>{point}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                    <div>
                        <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/land-prospect.png" alt="Aureus Alliance Holdings 250-Hectare Gold Mining Concession - Satellite View of Licensed Mining Area in Kadoma, Zimbabwe" className="rounded w-full"/>
                    </div>
                </div>
            </div>
        </section>
    );
};
const FinancialsSection: React.FC = () => {
    const [selectedGoldPrice, setSelectedGoldPrice] = useState(100);

    const tableData = [
        { site: 'Existing 250-hectare block', kg: 2400, val100: 240000000, val150: 360000000 },
        { site: 'Mutare Expansion (47 sites)', kg: 7600, val100: 760000000, val150: ********** },
    ];
    const totals = {
        kg: tableData.reduce((acc, row) => acc + row.kg, 0),
        val100: tableData.reduce((acc, row) => acc + row.val100, 0),
        val150: tableData.reduce((acc, row) => acc + row.val150, 0),
    };
    const headerClass = "p-4 text-gray-400 text-sm sm:text-base";
    const cellClass = "p-4 text-white text-sm sm:text-base";

    // Site Values Tab Component
    const SiteValuesTabs: React.FC = () => {
        const goldPrices = [
            { price: 100, label: '$100k/kg' },
            { price: 110, label: '$110k/kg' },
            { price: 120, label: '$120k/kg' },
            { price: 130, label: '$130k/kg' },
            { price: 140, label: '$140k/kg' },
            { price: 150, label: '$150k/kg' }
        ];

        const getCurrentData = () => {
            return tableData.map(row => ({
                ...row,
                currentValue: (row.kg * selectedGoldPrice * 1000) // kg * price per kg * 1000 (since price is in k)
            }));
        };

        const getCurrentTotal = () => {
            return totals.kg * selectedGoldPrice * 1000; // Total kg * price per kg * 1000
        };

        return (
            <div className="projection-tabs-container">
                {/* Tab Navigation */}
                <div className="tab-navigation">
                    <div className="tab-scroll-container">
                        {goldPrices.map(priceData => (
                            <button
                                key={priceData.price}
                                onClick={() => setSelectedGoldPrice(priceData.price)}
                                className={`tab-button ${selectedGoldPrice === priceData.price ? 'tab-active' : 'tab-inactive'}`}
                            >
                                {priceData.label}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Tab Content */}
                <div className="tab-content">
                    <div className="site-values-grid">
                        {getCurrentData().map(row => (
                            <div key={row.site} className="site-value-card">
                                <div className="site-header">
                                    <h4 className="site-title">{row.site}</h4>
                                    <div className="site-badge">{formatNumber(row.kg)} KG</div>
                                </div>
                                <div className="site-value">
                                    <span className="value-label">Site Value</span>
                                    <span className="value-amount">
                                        {formatNumber(row.currentValue, {style: 'currency', currency: 'USD', minimumFractionDigits: 0})}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Portfolio Summary */}
                    <div className="portfolio-summary">
                        <div className="summary-header">
                            <h4 className="summary-title">Portfolio Summary</h4>
                            <div className="summary-subtitle">Total across all mining sites</div>
                        </div>
                        <div className="summary-metrics">
                            <div className="summary-metric">
                                <span className="metric-value">{formatNumber(totals.kg)}</span>
                                <span className="metric-label">Total KG</span>
                            </div>
                            <div className="summary-divider"></div>
                            <div className="summary-metric primary">
                                <span className="metric-value">
                                    {formatNumber(getCurrentTotal(), {style: 'currency', currency: 'USD', minimumFractionDigits: 0})}
                                </span>
                                <span className="metric-label">Total Portfolio Value</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };
    
    return (
        <section id="financials" className="section">
            <div className="container">
                <div className="text-center mb-md">
                    <h2 className="heading-xl text-gold mb-xs">Financials & Share Value</h2>
                    <p className="text-lg max-w-2xl mx-auto">Conservative estimates based on geological data, providing a baseline for forecasting and share purchasing decisions.</p>
                </div>

                <div className="resource-estimates-modern">
                    <div className="resource-header">
                        <h3 className="resource-title">Resource Estimates (In Situ)</h3>
                        <div className="resource-subtitle">Conservative geological data baseline</div>
                    </div>

                    <div className="resource-grid">
                        <div className="resource-card">
                            <div className="resource-icon">📏</div>
                            <div className="resource-content">
                                <div className="resource-label">Area Extent</div>
                                <div className="resource-value">250 ha</div>
                            </div>
                        </div>

                        <div className="resource-card">
                            <div className="resource-icon">📐</div>
                            <div className="resource-content">
                                <div className="resource-label">Gravel Thickness</div>
                                <div className="resource-value">0.8 m</div>
                            </div>
                        </div>

                        <div className="resource-card">
                            <div className="resource-icon">📦</div>
                            <div className="resource-content">
                                <div className="resource-label">Volume (m³)</div>
                                <div className="resource-value">2,400,000</div>
                            </div>
                        </div>

                        <div className="resource-card">
                            <div className="resource-icon">⚖️</div>
                            <div className="resource-content">
                                <div className="resource-label">Grade (g/m³)</div>
                                <div className="resource-value">0.9</div>
                            </div>
                        </div>

                        <div className="resource-card highlight">
                            <div className="resource-icon">🏆</div>
                            <div className="resource-content">
                                <div className="resource-label">Potential Gold Output</div>
                                <div className="resource-value gold">2,160 kg</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Projected Site Values with Tab System */}
                <div className="projection-section-modern">
                    <div className="projection-header">
                        <h3 className="text-xl font-bold text-gold mb-2">Projected Site Values (Pre-Expense)</h3>
                        <p className="text-sm text-gray-400 mb-6">Based on figures from the prospectus - select gold price per kg</p>
                    </div>

                    <SiteValuesTabs />
                </div>
            </div>
        </section>
    );
};

const GoldDiggersClubSection: React.FC = () => {
    return (
        <section id="gold-diggers-club" className="section bg-gradient-to-b from-gray-900 to-black">
            <div className="container">
                {/* Header */}
                <div className="text-center mb-xl">
                    <h2 className="heading-hero text-gold mb-md">Gold Diggers Club</h2>
                    <div className="flex items-center justify-center gap-sm mb-md">
                        <span className="text-5xl font-black text-gold">$150,000</span>
                        <span className="text-xl text-gray-300 uppercase tracking-wide">BONUS POOL</span>
                    </div>
                    <p className="text-lg text-gray-300 max-w-3xl mx-auto">
                        Special leaderboard competition for the Top 10 Direct Sellers in the presale.
                    </p>
                    <p className="text-base text-gray-400 mt-sm">
                        Minimum $2,500 in direct referrals to qualify.
                    </p>
                </div>

                {/* Top Row - Two Cards Side by Side */}
                <div className="grid grid-cols-2 gap-xl mb-xl">
                    {/* How It Works Card */}
                    <div className="gold-diggers-card">
                        <div className="card-header">
                            <span className="card-icon">🎯</span>
                            <h3 className="card-title">How It Works</h3>
                        </div>
                        <div className="card-content">
                            <div className="step-item">
                                <div className="step-number">1</div>
                                <div className="step-content">
                                    <h4 className="step-title">Refer & Earn</h4>
                                    <p className="step-description">
                                        Build your network by referring new investors. Each qualified referral counts toward your ranking.
                                    </p>
                                </div>
                            </div>
                            <div className="step-item">
                                <div className="step-number">2</div>
                                <div className="step-content">
                                    <h4 className="step-title">Minimum Qualification</h4>
                                    <p className="step-description">
                                        Achieve minimum $2,500 in direct referral volume to qualify for bonus pool distribution.
                                    </p>
                                </div>
                            </div>
                            <div className="step-item">
                                <div className="step-number">3</div>
                                <div className="step-content">
                                    <h4 className="step-title">Climb the Rankings</h4>
                                    <p className="step-description">
                                        Your position is determined by total referral volume and network growth metrics.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Live Rankings Card */}
                    <div className="gold-diggers-card">
                        <div className="card-header">
                            <span className="card-icon">🏆</span>
                            <h3 className="card-title">Live Rankings</h3>
                            <span className="live-indicator">
                                <span className="live-dot"></span>
                                LIVE
                            </span>
                        </div>
                        <div className="card-content text-center">
                            <div className="competition-status">
                                <span className="status-icon">🏆</span>
                                <h4 className="status-title">Competition Starting Soon!</h4>
                                <p className="status-description">
                                    The Gold Diggers Club leaderboard will be populated as participants join the presale.
                                </p>
                                <p className="status-subtitle">
                                    Be the first to start building your network!
                                </p>
                            </div>

                            <div className="qualification-badge">
                                🎯 Minimum $2,500 in direct referrals to qualify
                            </div>
                        </div>
                    </div>
                </div>

                {/* Bottom Row - Prize Distribution and Stats */}
                <div className="grid grid-cols-3 gap-xl mb-xl">
                    {/* Prize Distribution Card - Takes 2 columns */}
                    <div className="col-span-2">
                        <div className="gold-diggers-card">
                            <div className="card-header">
                                <span className="card-icon">🏆</span>
                                <h3 className="card-title">Prize Distribution</h3>
                            </div>
                            <div className="card-content">
                                <div className="prize-list">
                                    <div className="prize-item prize-first">
                                        <span className="prize-rank">🥇 1st Place</span>
                                        <span className="prize-amount">$60,000</span>
                                    </div>
                                    <div className="prize-item prize-second">
                                        <span className="prize-rank">🥈 2nd Place</span>
                                        <span className="prize-amount">$30,000</span>
                                    </div>
                                    <div className="prize-item prize-third">
                                        <span className="prize-rank">🥉 3rd Place</span>
                                        <span className="prize-amount">$18,000</span>
                                    </div>
                                    <div className="prize-item prize-other">
                                        <span className="prize-rank">🏆 4th - 10th Place</span>
                                        <span className="prize-amount">$6,000 each</span>
                                    </div>
                                </div>
                                <div className="prize-note">
                                    Remaining pool distributed proportionally among top 10 qualified participants
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Live Stats - Takes 1 column */}
                    <div className="space-y-lg">
                        <div className="stat-card">
                            <div className="stat-number">0</div>
                            <div className="stat-label">Total Participants</div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-number">$0</div>
                            <div className="stat-label">Leading Volume</div>
                        </div>

                        {/* Join Competition Button */}
                        <div className="text-center mt-lg">
                            <a
                                href="https://t.me/AureusAllianceBot"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="btn-gold-diggers"
                            >
                                ⚡ Join the Competition
                            </a>
                            <p className="text-sm text-gray-400 mt-md">
                                Competition ends when presale reaches $1,000,000 total volume
                            </p>
                        </div>
                    </div>
                </div>


            </div>
        </section>
    );
};

const GallerySection: React.FC = () => {
    return (
        <section id="gallery" className="section">
            <div className="container">
                <div className="text-center mb-md">
                    <h2 className="heading-xl text-gold mb-xs">Proof of Concept & Gallery</h2>
                    <p className="text-lg max-w-2xl mx-auto">A visual journey through our on-site activities, team collaborations, and project milestones.</p>
                </div>
                <div className="gallery-container">
                    <EnhancedGallery
                        showCategories={false}
                        showSearch={false}
                        itemsPerPage={9}
                        showFeaturedFirst={true}
                        fallbackMode="static"
                        showConnectionStatus={false}
                    />
                </div>
            </div>
        </section>
    );
};


const Footer: React.FC<{setCurrentSection: (section: string) => void}> = ({setCurrentSection}) => (
    <footer className="footer-container">
        <div className="container">
            <div className="footer-content">
                <div className="footer-logo">AUREUS</div>

                <nav className="footer-nav">
                    <NavLink href="#about">About</NavLink>
                    <NavLink href="#highlights">Highlights</NavLink>
                    <NavLink href="#commission">Commission</NavLink>
                    <NavLink href="#gold-diggers-club">Gold Diggers Club</NavLink>
                    <NavLink href="#calculator">Calculator</NavLink>
                    <NavLink href="#project">Project</NavLink>
                    <NavLink href="#gallery">Gallery</NavLink>
                    <NavLink href="#charity">Charity</NavLink>
                </nav>

                <div className="footer-cta">
                    <a href="https://t.me/AureusAllianceBot" target="_blank" rel="noopener noreferrer" className="btn btn-primary">
                        Purchase Shares Now
                    </a>
                </div>

                <div className="footer-legal">
                    <div className="footer-legal-nav">
                        <a href="#privacy-policy" onClick={(e) => { e.preventDefault(); setCurrentSection('privacy-policy'); }}>Privacy Policy</a>
                        <a href="#terms-conditions" onClick={(e) => { e.preventDefault(); setCurrentSection('terms-conditions'); }}>Terms & Conditions</a>
                        <a href="#disclaimer" onClick={(e) => { e.preventDefault(); setCurrentSection('disclaimer'); }}>Disclaimer</a>
                    </div>
                    <p>&copy; {new Date().getFullYear()} Aureus Alliance Holdings. All rights reserved.</p>
                    <p>
                        Disclaimer: This website and the information contained herein are for informational purposes only and do not constitute an offer to sell or a solicitation of an offer to purchase any shares.
                    </p>
                </div>
            </div>
        </div>
    </footer>
);

const App: React.FC = () => {
    const [showAdmin, setShowAdmin] = useState(false);
    const [currentSection, setCurrentSection] = useState<string>('home');
    const [user, setUser] = useState<any>(null);
    const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

    // Live gold price integration
    const { price: liveGoldPrice, isLoading: goldPriceLoading, error: goldPriceError, refetch: refetchGoldPrice } = useGoldPrice();

    // Authentication handlers
    const handleLoginSuccess = (userData: any) => {
        console.log('✅ Login successful:', userData);
        setUser(userData);

        // Check if user needs profile completion
        if (userData.needsProfileCompletion || userData.user_metadata?.profile_completion_required) {
            console.log('🔄 User needs profile completion, redirecting...');
            setCurrentSection('profile-completion');
        } else {
            setCurrentSection('dashboard');
        }
    };

    const handleRegistrationSuccess = (userData: any) => {
        console.log('✅ Registration successful:', userData);
        setUser(userData);
        setCurrentSection('dashboard');
    };

    const handleSwitchToLogin = () => {
        setAuthMode('login');
        setCurrentSection('login');
    };

    const handleSwitchToRegister = () => {
        setAuthMode('register');
        setCurrentSection('register');
    };

    const handleProfileComplete = (completedUserData: any) => {
        console.log('✅ Profile completion successful:', completedUserData);
        setUser(completedUserData);
        setCurrentSection('dashboard');
    };

    const handleLogout = () => {
        setUser(null);
        setCurrentSection('home');
    };

    // Get content from context with fallbacks
    let getContent: (section: string, key: string, defaultValue?: any) => any;
    try {
        const context = useSiteContentContext();
        getContent = context.getContent;
    } catch (error) {
        console.warn('App: Site content context not available, using defaults');
        getContent = (section: string, key: string, defaultValue: any = '') => defaultValue;
    }

    // Get calculator content with fallbacks
    const calculatorTitle = getContent('calculator', 'title', 'Financial Calculator') || 'Financial Calculator';
    const calculatorSubtitle = getContent('calculator', 'subtitle', 'Experience the power of data-driven investment decisions with our interactive calculator') || 'Experience the power of data-driven investment decisions with our interactive calculator';



    // Check for admin parameter on mount
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('admin') === 'true') {
            setShowAdmin(true);
        }
    }, []);

    // Handle back to main site
    const handleBackToMain = () => {
        setShowAdmin(false);
        // Remove admin parameter from URL
        const url = new URL(window.location.href);
        url.searchParams.delete('admin');
        window.history.replaceState({}, '', url.toString());
    };

    // Show admin interface if admin=true
    if (showAdmin) {
        return <AdminRouter onBackToMain={handleBackToMain} />;
    }

    // Show legal pages if selected
    if (currentSection === 'privacy-policy') {
        return (
            <ErrorBoundary>
                <div className="legal-section">
                    <div className="container">
                        <button
                            className="back-button"
                            onClick={() => setCurrentSection('home')}
                        >
                            ← Back to Home
                        </button>
                        <PrivacyPolicy />
                    </div>
                </div>
            </ErrorBoundary>
        );
    }

    if (currentSection === 'terms-conditions') {
        return (
            <ErrorBoundary>
                <div className="legal-section">
                    <div className="container">
                        <button
                            className="back-button"
                            onClick={() => setCurrentSection('home')}
                        >
                            ← Back to Home
                        </button>
                        <TermsAndConditions />
                    </div>
                </div>
            </ErrorBoundary>
        );
    }

    if (currentSection === 'disclaimer') {
        return (
            <ErrorBoundary>
                <div className="legal-section">
                    <div className="container">
                        <button
                            className="back-button"
                            onClick={() => setCurrentSection('home')}
                        >
                            ← Back to Home
                        </button>
                        <LegalDisclaimer />
                    </div>
                </div>
            </ErrorBoundary>
        );
    }

    // Show login page if selected
    if (currentSection === 'login') {
        return (
            <ErrorBoundary>
                <div className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen">
                    {/* Enhanced Background Effects - Same as Homepage */}
                    <div className="fixed inset-0 overflow-hidden pointer-events-none">
                        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-500/10 rounded-full blur-3xl animate-pulse"></div>
                        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
                        <div className="absolute top-3/4 left-3/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
                        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-green-500/5 rounded-full blur-2xl animate-pulse" style={{animationDelay: '3s'}}></div>
                    </div>

                    {/* Header with Back Button */}
                    <header className="relative z-10 p-6">
                        <div className="container mx-auto">
                            <button
                                onClick={() => setCurrentSection('home')}
                                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200 group"
                            >
                                <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                                <span className="font-medium">Back</span>
                            </button>
                        </div>
                    </header>

                    {/* Main Login Content */}
                    <main className="relative z-10 px-4 py-8 min-h-[calc(100vh-120px)]">
                        <div className="w-full max-w-lg mx-auto">
                            {/* Logo and Branding */}
                            <div className="text-center mb-8">
                                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-6 shadow-2xl">
                                    <img
                                        src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                                        alt="Aureus Alliance Holdings Logo"
                                        className="w-12 h-12 object-contain"
                                    />
                                </div>
                                <h1 className="text-4xl md:text-5xl font-black uppercase tracking-wider text-white mb-4">
                                    Welcome Back
                                </h1>
                                <p className="text-xl text-gray-300 font-light">
                                    Sign in to your <span className="gold-text font-semibold">Aureus Alliance</span> account
                                </p>
                            </div>

                            {/* Email Login Form */}
                            <EmailLoginForm
                                onLoginSuccess={handleLoginSuccess}
                                onSwitchToRegister={handleSwitchToRegister}
                            />

                            {/* Footer Links */}
                            <div className="text-center mt-8 space-y-2">
                                <p className="text-gray-400">
                                    Need help? Message our{' '}
                                    <a
                                        href="https://t.me/AureusAllianceBot"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-yellow-400 hover:text-yellow-300 font-medium transition-colors duration-200"
                                    >
                                        Telegram Bot
                                    </a>
                                </p>
                                <p className="text-sm text-gray-500">
                                    Secure authentication powered by Supabase
                                </p>
                            </div>
                        </div>
                    </main>
                </div>
            </ErrorBoundary>
        );
    }

    // Show register page if selected
    if (currentSection === 'register') {
        return (
            <ErrorBoundary>
                <div className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen">
                    {/* Enhanced Background Effects - Same as Homepage */}
                    <div className="fixed inset-0 overflow-hidden pointer-events-none">
                        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-500/10 rounded-full blur-3xl animate-pulse"></div>
                        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
                        <div className="absolute top-3/4 left-3/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
                        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-green-500/5 rounded-full blur-2xl animate-pulse" style={{animationDelay: '3s'}}></div>
                    </div>

                    {/* Header with Back Button */}
                    <header className="relative z-10 p-6">
                        <div className="container mx-auto">
                            <button
                                onClick={() => setCurrentSection('home')}
                                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200 group"
                            >
                                <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                                <span className="font-medium">Back</span>
                            </button>
                        </div>
                    </header>

                    {/* Main Register Content */}
                    <main className="relative z-10 px-4 py-8">
                        <div className="w-full max-w-lg mx-auto">
                            {/* Logo and Branding */}
                            <div className="text-center mb-12">
                                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-6 shadow-2xl">
                                    <img
                                        src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                                        alt="Aureus Alliance Holdings Logo"
                                        className="w-12 h-12 object-contain"
                                    />
                                </div>
                                <h1 className="text-4xl md:text-5xl font-black uppercase tracking-wider text-white mb-4">
                                    Join Aureus Alliance
                                </h1>
                                <p className="text-xl text-gray-300 font-light">
                                    Start your <span className="gold-text font-semibold">gold share ownership</span> journey
                                </p>
                            </div>

                            {/* Email Registration Form */}
                            <EmailRegistrationForm
                                onRegistrationSuccess={handleRegistrationSuccess}
                                onSwitchToLogin={handleSwitchToLogin}
                            />

                            {/* Footer Links */}
                            <div className="text-center mt-8 space-y-2">
                                <p className="text-gray-400">
                                    Need help? Message our{' '}
                                    <a
                                        href="https://t.me/AureusAllianceBot"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-yellow-400 hover:text-yellow-300 font-medium transition-colors duration-200"
                                    >
                                        Telegram Bot
                                    </a>
                                </p>
                                <p className="text-sm text-gray-500">
                                    Secure registration powered by Supabase
                                </p>
                            </div>
                        </div>
                    </main>
                </div>
            </ErrorBoundary>
        );
    }



    // Show profile completion if user needs to complete profile
    if (currentSection === 'profile-completion' && user) {
        return (
            <ErrorBoundary>
                <ProfileCompletionForm
                    telegramUser={user.telegramUser}
                    onProfileComplete={handleProfileComplete}
                    onLogout={handleLogout}
                />
            </ErrorBoundary>
        );
    }

    // Show dashboard if user is logged in and dashboard is selected
    if (currentSection === 'dashboard' && user) {
        // Route guard: Prevent dashboard access if profile completion is needed
        if (user.needsProfileCompletion || user.user_metadata?.profile_completion_required) {
            console.log('🚫 Dashboard access blocked - profile completion required');
            setCurrentSection('profile-completion');
            return null; // Will re-render with profile completion
        }

        return (
            <ErrorBoundary>
                <UserDashboard
                    user={user}
                    onLogout={handleLogout}
                    onNavigate={setCurrentSection}
                />
            </ErrorBoundary>
        );
    }

    return (
        <ErrorBoundary>
            <div className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen">
                {/* Enhanced Background Effects */}
                <div className="fixed inset-0 overflow-hidden pointer-events-none">
                    <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-amber-500/5 rounded-full blur-3xl animate-pulse"></div>
                    <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
                    <div className="absolute top-3/4 left-3/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
                    <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-green-500/5 rounded-full blur-2xl animate-pulse" style={{animationDelay: '3s'}}></div>
                </div>

                <Header setCurrentSection={setCurrentSection} />

                <main className="relative z-10">
                    {/* Hero Section */}
                    <HeroSection />

                    {/* Main Content Flow */}
                    <div className="relative">
                        <AboutAureusSection />
                        <KeyHighlightsSection />
                        <CharitySection />
                        <NetworkerCommissionSection />
                        <GoldDiggersClubSection />

                        <section id="calculator" className="section">
                            <div className="container">
                                <div className="text-center mb-md">
                                    <h2 className="heading-xl text-gold mb-xs">{calculatorTitle}</h2>
                                    <p className="text-lg max-w-2xl mx-auto">{calculatorSubtitle}</p>
                                </div>
                                <Calculator
                                    liveGoldPrice={liveGoldPrice}
                                    goldPriceLoading={goldPriceLoading}
                                    goldPriceError={goldPriceError}
                                />
                            </div>
                        </section>

                        <ProjectSection />
                        <FinancialsSection />
                        <GallerySection />
                    </div>

                    <Footer setCurrentSection={setCurrentSection} />
                </main>
            </div>
        </ErrorBoundary>
    );
};

export default App;

