-- Fix authentication user mapping for web dashboard
-- This adds the missing link between Supabase auth users and database users

-- 1. Add auth_user_id column to users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS auth_user_id UUID;

-- 2. Create index for better performance
CREATE INDEX IF NOT EXISTS idx_users_auth_user_id ON public.users(auth_user_id);

-- 3. Update RLS policies to use the correct mapping
-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view own share purchases" ON public.aureus_share_purchases;
DROP POLICY IF EXISTS "Users can insert own share purchases" ON public.aureus_share_purchases;
DROP POLICY IF EXISTS "Users can view own commission balance" ON public.commission_balances;
DROP POLICY IF EXISTS "Users can view own commission transactions" ON public.commission_transactions;
DROP POLICY IF EXISTS "Users can view own withdrawal requests" ON public.commission_withdrawal_requests;
DROP POLICY IF EXISTS "Users can create own withdrawal requests" ON public.commission_withdrawal_requests;
DROP POLICY IF EXISTS "Users can view own commission usage" ON public.commission_usage;

-- Create new policies that work with auth_user_id mapping
CREATE POLICY "Users can view own share purchases" ON public.aureus_share_purchases
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own share purchases" ON public.aureus_share_purchases
  FOR INSERT WITH CHECK (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view own commission balance" ON public.commission_balances
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view own commission transactions" ON public.commission_transactions
  FOR SELECT USING (
    referrer_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view own withdrawal requests" ON public.commission_withdrawal_requests
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create own withdrawal requests" ON public.commission_withdrawal_requests
  FOR INSERT WITH CHECK (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view own commission usage" ON public.commission_usage
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- 4. Also fix referrals table policy
DROP POLICY IF EXISTS "Users can view own referrals" ON public.referrals;
CREATE POLICY "Users can view own referrals" ON public.referrals
  FOR SELECT USING (
    referrer_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- 5. Grant necessary permissions (ensure they exist)
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT ALL ON public.aureus_share_purchases TO authenticated;
GRANT ALL ON public.commission_balances TO authenticated;
GRANT ALL ON public.commission_transactions TO authenticated;
GRANT ALL ON public.commission_withdrawal_requests TO authenticated;
GRANT ALL ON public.commission_usage TO authenticated;
GRANT SELECT ON public.referrals TO authenticated;

-- 6. Create helper function to update auth_user_id
CREATE OR REPLACE FUNCTION public.update_user_auth_id(p_email TEXT, p_auth_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.users 
  SET auth_user_id = p_auth_user_id 
  WHERE email = p_email AND auth_user_id IS NULL;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.update_user_auth_id(TEXT, UUID) TO authenticated;

COMMENT ON FUNCTION public.update_user_auth_id IS 'Updates the auth_user_id for a user based on email';
