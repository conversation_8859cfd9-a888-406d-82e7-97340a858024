import React, { useState, useEffect } from 'react'

interface MarketingToolkitProps {
  user: any
  getReferralUsername: (user: any) => string
}

// Campaign platform configurations
const CAMPAIGN_PLATFORMS = [
  { id: 'facebook', name: 'Facebook', icon: '📘', color: '#1877F2', description: 'Facebook posts and ads' },
  { id: 'instagram', name: 'Instagram', icon: '📷', color: '#E4405F', description: 'Instagram stories and posts' },
  { id: 'twitter', name: 'Twitter/X', icon: '🐦', color: '#1DA1F2', description: 'Twitter posts and threads' },
  { id: 'linkedin', name: 'LinkedIn', icon: '💼', color: '#0A66C2', description: 'Professional networking' },
  { id: 'youtube', name: 'YouTube', icon: '📺', color: '#FF0000', description: 'Video descriptions and comments' },
  { id: 'email', name: 'Email', icon: '📧', color: '#34495E', description: 'Email newsletters and campaigns' },
  { id: 'whatsapp', name: 'WhatsApp', icon: '💬', color: '#25D366', description: 'WhatsApp messages and status' },
  { id: 'telegram', name: 'Telegram', icon: '✈️', color: '#0088CC', description: 'Telegram channels and groups' },
  { id: 'tiktok', name: 'TikTok', icon: '🎵', color: '#000000', description: 'TikTok video descriptions' },
  { id: 'reddit', name: 'Reddit', icon: '🤖', color: '#FF4500', description: 'Reddit posts and comments' }
]

export const MarketingToolkit: React.FC<MarketingToolkitProps> = ({ user, getReferralUsername }) => {
  const [activeTab, setActiveTab] = useState<'generator' | 'analytics' | 'content' | 'training'>('generator')
  const [selectedCampaign, setSelectedCampaign] = useState<string>('')
  const [customCampaign, setCustomCampaign] = useState<string>('')
  const [generatedLinks, setGeneratedLinks] = useState<{ [key: string]: string }>({})
  const [copiedLink, setCopiedLink] = useState<string>('')

  // Generate referral link with campaign tracking
  const generateReferralLink = (campaignId: string, customName?: string) => {
    const username = getReferralUsername(user)
    const campaign = customName || campaignId
    return `https://aureus.africa/register?ref=${username}&campaign=${campaign}`
  }

  // Copy link to clipboard
  const copyToClipboard = async (link: string, campaignName: string) => {
    try {
      await navigator.clipboard.writeText(link)
      setCopiedLink(campaignName)
      setTimeout(() => setCopiedLink(''), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  // Generate all campaign links
  useEffect(() => {
    const links: { [key: string]: string } = {}
    CAMPAIGN_PLATFORMS.forEach(platform => {
      links[platform.id] = generateReferralLink(platform.id)
    })
    if (customCampaign) {
      links['custom'] = generateReferralLink('custom', customCampaign)
    }
    setGeneratedLinks(links)
  }, [user, customCampaign])

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '16px',
      padding: '32px',
      marginBottom: '32px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <h2 style={{ 
          fontSize: '28px', 
          fontWeight: 'bold', 
          color: 'white', 
          margin: '0 0 12px 0',
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          🚀 Marketing Toolkit
        </h2>
        <p style={{ color: '#9ca3af', fontSize: '16px', margin: 0 }}>
          Professional-grade marketing tools to maximize your referral success
        </p>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '32px',
        borderBottom: '1px solid #374151',
        paddingBottom: '16px'
      }}>
        {[
          { id: 'generator', label: '🔗 Link Generator', desc: 'Create campaign links' },
          { id: 'analytics', label: '📊 Analytics', desc: 'Track performance' },
          { id: 'content', label: '✨ AI Content', desc: 'Generate marketing content' },
          { id: 'training', label: '🎓 Training', desc: 'Learn best practices' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            style={{
              flex: 1,
              padding: '16px',
              backgroundColor: activeTab === tab.id ? 'rgba(59, 130, 246, 0.2)' : 'transparent',
              border: activeTab === tab.id ? '1px solid #3b82f6' : '1px solid #374151',
              borderRadius: '12px',
              color: activeTab === tab.id ? '#60a5fa' : '#9ca3af',
              cursor: 'pointer',
              transition: 'all 0.2s',
              textAlign: 'center'
            }}
          >
            <div style={{ fontSize: '14px', fontWeight: '600', marginBottom: '4px' }}>
              {tab.label}
            </div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>
              {tab.desc}
            </div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'generator' && (
        <div>
          {/* Custom Campaign Input */}
          <div style={{ marginBottom: '24px' }}>
            <label style={{ 
              display: 'block', 
              color: '#d1d5db', 
              fontSize: '14px', 
              fontWeight: '500', 
              marginBottom: '8px' 
            }}>
              Custom Campaign Name (Optional)
            </label>
            <input
              type="text"
              value={customCampaign}
              onChange={(e) => setCustomCampaign(e.target.value)}
              placeholder="e.g., summer_promo, webinar_2024, newsletter_jan"
              style={{
                width: '100%',
                padding: '12px 16px',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                border: '1px solid #4b5563',
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px'
              }}
            />
          </div>

          {/* Campaign Platform Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '16px'
          }}>
            {CAMPAIGN_PLATFORMS.map(platform => (
              <div
                key={platform.id}
                style={{
                  backgroundColor: 'rgba(55, 65, 81, 0.5)',
                  borderRadius: '12px',
                  padding: '20px',
                  border: '1px solid #4b5563'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <span style={{ fontSize: '24px', marginRight: '12px' }}>{platform.icon}</span>
                  <div>
                    <h3 style={{ 
                      color: 'white', 
                      fontSize: '16px', 
                      fontWeight: '600', 
                      margin: '0 0 4px 0' 
                    }}>
                      {platform.name}
                    </h3>
                    <p style={{ 
                      color: '#9ca3af', 
                      fontSize: '12px', 
                      margin: 0 
                    }}>
                      {platform.description}
                    </p>
                  </div>
                </div>

                <div style={{
                  backgroundColor: 'rgba(31, 41, 55, 0.8)',
                  borderRadius: '8px',
                  padding: '12px',
                  marginBottom: '12px',
                  fontSize: '13px',
                  color: '#d1d5db',
                  wordBreak: 'break-all',
                  fontFamily: 'monospace'
                }}>
                  {generatedLinks[platform.id]}
                </div>

                <button
                  onClick={() => copyToClipboard(generatedLinks[platform.id], platform.name)}
                  style={{
                    width: '100%',
                    padding: '10px 16px',
                    backgroundColor: copiedLink === platform.name ? '#10b981' : platform.color,
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                >
                  {copiedLink === platform.name ? '✅ Copied!' : `📋 Copy ${platform.name} Link`}
                </button>
              </div>
            ))}

            {/* Custom Campaign Link */}
            {customCampaign && (
              <div style={{
                backgroundColor: 'rgba(139, 69, 19, 0.2)',
                borderRadius: '12px',
                padding: '20px',
                border: '1px solid #8b4513'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <span style={{ fontSize: '24px', marginRight: '12px' }}>🎯</span>
                  <div>
                    <h3 style={{ 
                      color: 'white', 
                      fontSize: '16px', 
                      fontWeight: '600', 
                      margin: '0 0 4px 0' 
                    }}>
                      Custom: {customCampaign}
                    </h3>
                    <p style={{ 
                      color: '#9ca3af', 
                      fontSize: '12px', 
                      margin: 0 
                    }}>
                      Your personalized campaign
                    </p>
                  </div>
                </div>

                <div style={{
                  backgroundColor: 'rgba(31, 41, 55, 0.8)',
                  borderRadius: '8px',
                  padding: '12px',
                  marginBottom: '12px',
                  fontSize: '13px',
                  color: '#d1d5db',
                  wordBreak: 'break-all',
                  fontFamily: 'monospace'
                }}>
                  {generatedLinks['custom']}
                </div>

                <button
                  onClick={() => copyToClipboard(generatedLinks['custom'], 'Custom')}
                  style={{
                    width: '100%',
                    padding: '10px 16px',
                    backgroundColor: copiedLink === 'Custom' ? '#10b981' : '#8b4513',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                >
                  {copiedLink === 'Custom' ? '✅ Copied!' : '📋 Copy Custom Link'}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
          <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
            Analytics Dashboard
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '16px', marginBottom: '24px' }}>
            Campaign performance tracking coming soon! This will include:
          </p>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
            gap: '16px',
            textAlign: 'left'
          }}>
            {[
              '📈 Click-through rates by campaign',
              '🎯 Conversion tracking and optimization',
              '🏆 Top-performing platforms and content',
              '📅 Real-time performance metrics',
              '💡 AI-powered optimization suggestions',
              '🔄 A/B testing results and insights'
            ].map((feature, index) => (
              <div key={index} style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '16px',
                borderRadius: '8px',
                color: '#d1d5db'
              }}>
                {feature}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'content' && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>✨</div>
          <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
            AI Content Generator
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '16px', marginBottom: '24px' }}>
            AI-powered marketing content creation coming soon! Features will include:
          </p>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
            gap: '16px',
            textAlign: 'left'
          }}>
            {[
              '📝 Social media post templates',
              '📧 Email marketing campaigns',
              '🎨 Custom graphics and visuals',
              '📱 QR codes for offline marketing',
              '🎯 Platform-specific content optimization',
              '🔄 Multiple variations for A/B testing'
            ].map((feature, index) => (
              <div key={index} style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '16px',
                borderRadius: '8px',
                color: '#d1d5db'
              }}>
                {feature}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'training' && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎓</div>
          <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
            Training & Resources
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '16px', marginBottom: '24px' }}>
            Comprehensive training resources coming soon! This will include:
          </p>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
            gap: '16px',
            textAlign: 'left'
          }}>
            {[
              '🎥 Video tutorials for each platform',
              '📚 Best practices and strategies',
              '🏆 Success stories from top performers',
              '📅 Live training sessions and webinars',
              '💬 Community networking tips',
              '📊 Data-driven marketing insights'
            ].map((feature, index) => (
              <div key={index} style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '16px',
                borderRadius: '8px',
                color: '#d1d5db'
              }}>
                {feature}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
