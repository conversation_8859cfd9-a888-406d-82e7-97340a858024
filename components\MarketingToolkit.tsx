import React, { useState, useEffect } from 'react'

interface MarketingToolkitProps {
  user: any
  getReferralUsername: (user: any) => string
}

// Campaign platform configurations
const CAMPAIGN_PLATFORMS = [
  { id: 'facebook', name: 'Facebook', icon: '📘', color: '#1877F2', description: 'Facebook posts and ads' },
  { id: 'instagram', name: 'Instagram', icon: '📷', color: '#E4405F', description: 'Instagram stories and posts' },
  { id: 'twitter', name: 'Twitter/X', icon: '🐦', color: '#1DA1F2', description: 'Twitter posts and threads' },
  { id: 'linkedin', name: 'LinkedIn', icon: '💼', color: '#0A66C2', description: 'Professional networking' },
  { id: 'youtube', name: 'YouTube', icon: '📺', color: '#FF0000', description: 'Video descriptions and comments' },
  { id: 'email', name: 'Email', icon: '📧', color: '#34495E', description: 'Email newsletters and campaigns' },
  { id: 'whatsapp', name: 'WhatsApp', icon: '💬', color: '#25D366', description: 'WhatsApp messages and status' },
  { id: 'telegram', name: 'Telegram', icon: '✈️', color: '#0088CC', description: 'Telegram channels and groups' },
  { id: 'tiktok', name: 'TikTok', icon: '🎵', color: '#000000', description: 'TikTok video descriptions' },
  { id: 'reddit', name: 'Reddit', icon: '🤖', color: '#FF4500', description: 'Reddit posts and comments' }
]

export const MarketingToolkit: React.FC<MarketingToolkitProps> = ({ user, getReferralUsername }) => {
  const [activeTab, setActiveTab] = useState<'generator' | 'analytics' | 'content' | 'training'>('generator')
  const [selectedCampaign, setSelectedCampaign] = useState<string>('')
  const [customCampaign, setCustomCampaign] = useState<string>('')
  const [generatedLinks, setGeneratedLinks] = useState<{ [key: string]: string }>({})
  const [copiedLink, setCopiedLink] = useState<string>('')
  const [savedCampaigns, setSavedCampaigns] = useState<string[]>([])
  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState('')
  const [referralAnalytics, setReferralAnalytics] = useState<any[]>([])
  const [analyticsLoading, setAnalyticsLoading] = useState(false)
  const [linkAnalytics, setLinkAnalytics] = useState<{ [key: string]: any }>({})
  const [analyticsLoaded, setAnalyticsLoaded] = useState(false)

  // Generate referral link with campaign tracking
  const generateReferralLink = (campaignId: string, customName?: string) => {
    const username = getReferralUsername(user)
    const campaign = customName || campaignId
    return `https://aureus.africa/register?ref=${username}&campaign=${campaign}`
  }

  // Copy link to clipboard
  const copyToClipboard = async (link: string, campaignName: string) => {
    try {
      await navigator.clipboard.writeText(link)
      setCopiedLink(campaignName)
      setTimeout(() => setCopiedLink(''), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  // Get analytics for a specific campaign
  const getCampaignStats = (campaignId: string) => {
    const stats = linkAnalytics[campaignId] || linkAnalytics[campaignId.toLowerCase()]
    return {
      clicks: stats?.clicks || 0,
      registrations: stats?.registrations || 0,
      conversions: stats?.conversions || 0,
      revenue: stats?.revenue || 0
    }
  }

  // Remove a saved campaign
  const removeSavedCampaign = (campaignToRemove: string) => {
    try {
      const existingCampaigns = JSON.parse(localStorage.getItem('savedCampaigns') || '[]')
      const updatedCampaigns = existingCampaigns.filter((campaign: string) => campaign !== campaignToRemove)
      localStorage.setItem('savedCampaigns', JSON.stringify(updatedCampaigns))
      setSavedCampaigns(updatedCampaigns)
      console.log('🗑️ Campaign removed:', campaignToRemove)
    } catch (error) {
      console.error('Failed to remove campaign:', error)
    }
  }

  // Save custom campaign
  const saveCustomCampaign = async () => {
    if (!customCampaign.trim()) {
      setSaveMessage('Please enter a campaign name')
      setTimeout(() => setSaveMessage(''), 3000)
      return
    }

    setIsSaving(true)
    try {
      // Save to localStorage for now (can be enhanced to save to database)
      const existingCampaigns = JSON.parse(localStorage.getItem('savedCampaigns') || '[]')
      const trimmedCampaign = customCampaign.trim()

      if (!existingCampaigns.includes(trimmedCampaign)) {
        const updatedCampaigns = [...existingCampaigns, trimmedCampaign]
        localStorage.setItem('savedCampaigns', JSON.stringify(updatedCampaigns))
        setSavedCampaigns(updatedCampaigns)
        console.log('✅ Campaign saved:', trimmedCampaign)
        console.log('📋 Updated campaigns list:', updatedCampaigns)
      } else {
        console.log('⚠️ Campaign already exists:', trimmedCampaign)
      }

      setSaveMessage('✅ Campaign saved successfully!')
      setTimeout(() => setSaveMessage(''), 3000)
    } catch (error) {
      console.error('Failed to save campaign:', error)
      setSaveMessage('❌ Failed to save campaign')
      setTimeout(() => setSaveMessage(''), 3000)
    } finally {
      setIsSaving(false)
    }
  }

  // Load referral analytics - temporarily disabled to fix 404 error
  const loadReferralAnalytics = async () => {
    setAnalyticsLoading(true)
    try {
      // TODO: Re-enable when referral_analytics table is properly set up
      console.log('📊 Referral analytics temporarily disabled')
      setReferralAnalytics([])
    } catch (error) {
      console.error('Failed to load analytics:', error)
    } finally {
      setAnalyticsLoading(false)
    }
  }

  // Load link-specific analytics for display on each link - temporarily disabled
  const loadLinkAnalytics = async () => {
    if (analyticsLoaded) return

    try {
      // TODO: Re-enable when referral_analytics table is properly set up
      console.log('📊 Link analytics temporarily disabled')
      setLinkAnalytics({})
      setAnalyticsLoaded(true)
    } catch (error) {
      console.error('Failed to load link analytics:', error)
    }
  }

  // Export analytics data - temporarily disabled
  const exportAnalytics = async () => {
    try {
      // TODO: Re-enable when referral_analytics table is properly set up
      console.log('📊 Export analytics temporarily disabled')
      return

      if (exportResult.success) {
        // Create and download CSV file
        const blob = new Blob([exportResult.data], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = exportResult.filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        setSaveMessage('✅ Analytics exported successfully!')
        setTimeout(() => setSaveMessage(''), 3000)
      } else {
        setSaveMessage('❌ Failed to export analytics')
        setTimeout(() => setSaveMessage(''), 3000)
      }
    } catch (error) {
      console.error('Failed to export analytics:', error)
      setSaveMessage('❌ Export failed')
      setTimeout(() => setSaveMessage(''), 3000)
    }
  }

  // Generate all campaign links
  useEffect(() => {
    const links: { [key: string]: string } = {}
    CAMPAIGN_PLATFORMS.forEach(platform => {
      links[platform.id] = generateReferralLink(platform.id)
    })
    if (customCampaign) {
      links['custom'] = generateReferralLink('custom', customCampaign)
    }
    setGeneratedLinks(links)
  }, [user, customCampaign])

  // Load saved campaigns on component mount
  useEffect(() => {
    const saved = JSON.parse(localStorage.getItem('savedCampaigns') || '[]')
    setSavedCampaigns(saved)
  }, [])

  // Load analytics when analytics tab is active
  useEffect(() => {
    if (activeTab === 'analytics') {
      loadReferralAnalytics()
    }
  }, [activeTab, user])

  // Load link analytics when generator tab is active
  useEffect(() => {
    if (activeTab === 'generator') {
      loadLinkAnalytics()
    }
  }, [activeTab, user])

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '16px',
      padding: '32px',
      marginBottom: '32px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <h2 style={{ 
          fontSize: '28px', 
          fontWeight: 'bold', 
          color: 'white', 
          margin: '0 0 12px 0',
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          🚀 Marketing Toolkit
        </h2>
        <p style={{ color: '#9ca3af', fontSize: '16px', margin: 0 }}>
          Professional-grade marketing tools to maximize your referral success
        </p>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '32px',
        borderBottom: '1px solid #374151',
        paddingBottom: '16px'
      }}>
        {[
          { id: 'generator', label: '🔗 Link Generator', desc: 'Create campaign links' },
          { id: 'analytics', label: '📊 Analytics', desc: 'Track performance' },
          { id: 'content', label: '✨ AI Content', desc: 'Generate marketing content' },
          { id: 'training', label: '🎓 Training', desc: 'Learn best practices' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            style={{
              flex: 1,
              padding: '16px',
              backgroundColor: activeTab === tab.id ? 'rgba(59, 130, 246, 0.2)' : 'transparent',
              border: activeTab === tab.id ? '1px solid #3b82f6' : '1px solid #374151',
              borderRadius: '12px',
              color: activeTab === tab.id ? '#60a5fa' : '#9ca3af',
              cursor: 'pointer',
              transition: 'all 0.2s',
              textAlign: 'center'
            }}
          >
            <div style={{ fontSize: '14px', fontWeight: '600', marginBottom: '4px' }}>
              {tab.label}
            </div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>
              {tab.desc}
            </div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'generator' && (
        <div>
          {/* Custom Campaign Input */}
          <div style={{ marginBottom: '24px' }}>
            <label style={{
              display: 'block',
              color: '#d1d5db',
              fontSize: '14px',
              fontWeight: '500',
              marginBottom: '8px'
            }}>
              Custom Campaign Name (Optional)
            </label>
            <div style={{ display: 'flex', gap: '12px', alignItems: 'flex-start' }}>
              <input
                type="text"
                value={customCampaign}
                onChange={(e) => setCustomCampaign(e.target.value)}
                placeholder="e.g., summer_promo, webinar_2024, newsletter_jan"
                style={{
                  flex: 1,
                  padding: '12px 16px',
                  backgroundColor: 'rgba(55, 65, 81, 0.5)',
                  border: '1px solid #4b5563',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px'
                }}
              />
              <button
                onClick={saveCustomCampaign}
                disabled={isSaving || !customCampaign.trim()}
                style={{
                  padding: '12px 20px',
                  backgroundColor: isSaving ? '#6b7280' : '#059669',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: isSaving || !customCampaign.trim() ? 'not-allowed' : 'pointer',
                  whiteSpace: 'nowrap',
                  opacity: isSaving || !customCampaign.trim() ? 0.6 : 1,
                  transition: 'all 0.2s'
                }}
              >
                {isSaving ? '💾 Saving...' : '💾 Save Filter'}
              </button>
            </div>
            {saveMessage && (
              <p style={{
                marginTop: '8px',
                fontSize: '12px',
                color: saveMessage.includes('✅') ? '#10b981' : '#ef4444',
                fontWeight: '500'
              }}>
                {saveMessage}
              </p>
            )}
            {savedCampaigns.length > 0 && (
              <div style={{ marginTop: '12px' }}>
                <p style={{ fontSize: '12px', color: '#9ca3af', marginBottom: '6px' }}>
                  Saved Campaigns:
                </p>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px' }}>
                  {savedCampaigns.map((campaign, index) => (
                    <div
                      key={index}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        border: '1px solid #3b82f6',
                        borderRadius: '4px',
                        overflow: 'hidden'
                      }}
                    >
                      <button
                        onClick={() => setCustomCampaign(campaign)}
                        style={{
                          padding: '4px 8px',
                          backgroundColor: 'transparent',
                          border: 'none',
                          color: '#60a5fa',
                          fontSize: '11px',
                          cursor: 'pointer'
                        }}
                      >
                        {campaign}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          removeSavedCampaign(campaign)
                        }}
                        style={{
                          padding: '2px 6px',
                          backgroundColor: 'rgba(239, 68, 68, 0.2)',
                          border: 'none',
                          borderLeft: '1px solid #3b82f6',
                          color: '#f87171',
                          fontSize: '10px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center'
                        }}
                        title="Remove campaign"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Campaign Platform Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '16px'
          }}>
            {CAMPAIGN_PLATFORMS.map(platform => (
              <div
                key={platform.id}
                style={{
                  backgroundColor: 'rgba(55, 65, 81, 0.5)',
                  borderRadius: '12px',
                  padding: '20px',
                  border: '1px solid #4b5563'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <span style={{ fontSize: '24px', marginRight: '12px' }}>{platform.icon}</span>
                  <div>
                    <h3 style={{ 
                      color: 'white', 
                      fontSize: '16px', 
                      fontWeight: '600', 
                      margin: '0 0 4px 0' 
                    }}>
                      {platform.name}
                    </h3>
                    <p style={{ 
                      color: '#9ca3af', 
                      fontSize: '12px', 
                      margin: 0 
                    }}>
                      {platform.description}
                    </p>
                  </div>
                </div>

                <div style={{
                  backgroundColor: 'rgba(31, 41, 55, 0.8)',
                  borderRadius: '8px',
                  padding: '12px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#d1d5db',
                  wordBreak: 'break-all',
                  fontFamily: 'monospace'
                }}>
                  {generatedLinks[platform.id]}
                </div>

                {/* Usage Statistics */}
                {(() => {
                  const stats = getCampaignStats(platform.id)
                  return (
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '12px',
                      padding: '8px 12px',
                      backgroundColor: 'rgba(55, 65, 81, 0.5)',
                      borderRadius: '6px',
                      fontSize: '11px'
                    }}>
                      <div style={{ display: 'flex', gap: '12px' }}>
                        <span style={{ color: '#60a5fa' }}>
                          👆 {stats.clicks} clicks
                        </span>
                        <span style={{ color: '#10b981' }}>
                          👥 {stats.registrations} signups
                        </span>
                        <span style={{ color: '#f59e0b' }}>
                          💰 {stats.conversions} sales
                        </span>
                      </div>
                      {stats.revenue > 0 && (
                        <span style={{ color: '#8b5cf6', fontWeight: '500' }}>
                          ${stats.revenue.toFixed(2)}
                        </span>
                      )}
                    </div>
                  )
                })()}

                <button
                  onClick={() => copyToClipboard(generatedLinks[platform.id], platform.name)}
                  style={{
                    width: '100%',
                    padding: '10px 16px',
                    backgroundColor: copiedLink === platform.name ? '#10b981' : platform.color,
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                >
                  {copiedLink === platform.name ? '✅ Copied!' : `📋 Copy ${platform.name} Link`}
                </button>
              </div>
            ))}

            {/* Custom Campaign Link */}
            {customCampaign && (
              <div style={{
                backgroundColor: 'rgba(139, 69, 19, 0.2)',
                borderRadius: '12px',
                padding: '20px',
                border: '1px solid #8b4513'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <span style={{ fontSize: '24px', marginRight: '12px' }}>🎯</span>
                  <div>
                    <h3 style={{ 
                      color: 'white', 
                      fontSize: '16px', 
                      fontWeight: '600', 
                      margin: '0 0 4px 0' 
                    }}>
                      Custom: {customCampaign}
                    </h3>
                    <p style={{ 
                      color: '#9ca3af', 
                      fontSize: '12px', 
                      margin: 0 
                    }}>
                      Your personalized campaign
                    </p>
                  </div>
                </div>

                <div style={{
                  backgroundColor: 'rgba(31, 41, 55, 0.8)',
                  borderRadius: '8px',
                  padding: '12px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#d1d5db',
                  wordBreak: 'break-all',
                  fontFamily: 'monospace'
                }}>
                  {generatedLinks['custom']}
                </div>

                {/* Custom Campaign Usage Statistics */}
                {(() => {
                  const stats = getCampaignStats(customCampaign)
                  return (
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '12px',
                      padding: '8px 12px',
                      backgroundColor: 'rgba(139, 69, 19, 0.3)',
                      borderRadius: '6px',
                      fontSize: '11px'
                    }}>
                      <div style={{ display: 'flex', gap: '12px' }}>
                        <span style={{ color: '#60a5fa' }}>
                          👆 {stats.clicks} clicks
                        </span>
                        <span style={{ color: '#10b981' }}>
                          👥 {stats.registrations} signups
                        </span>
                        <span style={{ color: '#f59e0b' }}>
                          💰 {stats.conversions} sales
                        </span>
                      </div>
                      {stats.revenue > 0 && (
                        <span style={{ color: '#8b5cf6', fontWeight: '500' }}>
                          ${stats.revenue.toFixed(2)}
                        </span>
                      )}
                    </div>
                  )
                })()}

                <button
                  onClick={() => copyToClipboard(generatedLinks['custom'], 'Custom')}
                  style={{
                    width: '100%',
                    padding: '10px 16px',
                    backgroundColor: copiedLink === 'Custom' ? '#10b981' : '#8b4513',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                >
                  {copiedLink === 'Custom' ? '✅ Copied!' : '📋 Copy Custom Link'}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div>
          {analyticsLoading ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
              <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
                Loading Analytics...
              </h3>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #374151',
                borderTop: '4px solid #f59e0b',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto'
              }}></div>
            </div>
          ) : (
            <div>
              {/* Analytics Header */}
              <div style={{ marginBottom: '32px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <h3 style={{ color: 'white', fontSize: '24px', margin: 0 }}>
                    📊 Referral Analytics Dashboard
                  </h3>
                  <button
                    onClick={exportAnalytics}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#059669',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '12px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px'
                    }}
                  >
                    📥 Export CSV
                  </button>
                </div>
                <p style={{ color: '#9ca3af', fontSize: '14px', textAlign: 'center', margin: 0 }}>
                  Track your referral performance and optimize your campaigns
                </p>
                {saveMessage && (
                  <p style={{
                    marginTop: '8px',
                    fontSize: '12px',
                    color: saveMessage.includes('✅') ? '#10b981' : '#ef4444',
                    fontWeight: '500',
                    textAlign: 'center'
                  }}>
                    {saveMessage}
                  </p>
                )}
              </div>

              {/* Summary Cards */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '16px',
                marginBottom: '32px'
              }}>
                {[
                  {
                    title: 'Total Clicks',
                    value: referralAnalytics.reduce((sum, item) => sum + (item.clicks || 0), 0),
                    icon: '👆',
                    color: '#3b82f6'
                  },
                  {
                    title: 'Registrations',
                    value: referralAnalytics.reduce((sum, item) => sum + (item.registrations || 0), 0),
                    icon: '👥',
                    color: '#10b981'
                  },
                  {
                    title: 'Conversions',
                    value: referralAnalytics.reduce((sum, item) => sum + (item.conversions || 0), 0),
                    icon: '💰',
                    color: '#f59e0b'
                  },
                  {
                    title: 'Total Revenue',
                    value: `$${referralAnalytics.reduce((sum, item) => sum + (item.total_revenue || 0), 0).toFixed(2)}`,
                    icon: '📈',
                    color: '#8b5cf6'
                  }
                ].map((stat, index) => (
                  <div
                    key={index}
                    style={{
                      backgroundColor: 'rgba(55, 65, 81, 0.5)',
                      borderRadius: '12px',
                      padding: '20px',
                      border: '1px solid #4b5563',
                      textAlign: 'center'
                    }}
                  >
                    <div style={{ fontSize: '32px', marginBottom: '8px' }}>{stat.icon}</div>
                    <h4 style={{
                      color: 'white',
                      fontSize: '24px',
                      fontWeight: 'bold',
                      margin: '0 0 4px 0'
                    }}>
                      {stat.value}
                    </h4>
                    <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>
                      {stat.title}
                    </p>
                  </div>
                ))}
              </div>

              {/* Campaign Performance Table */}
              {referralAnalytics.length > 0 ? (
                <div style={{
                  backgroundColor: 'rgba(55, 65, 81, 0.5)',
                  borderRadius: '12px',
                  padding: '24px',
                  border: '1px solid #4b5563'
                }}>
                  <h4 style={{ color: 'white', fontSize: '18px', marginBottom: '16px' }}>
                    📊 Campaign Performance
                  </h4>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                      <thead>
                        <tr style={{ borderBottom: '1px solid #4b5563' }}>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'left' }}>Campaign</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'center' }}>Clicks</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'center' }}>Registrations</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'center' }}>Conversions</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'center' }}>Conv. Rate</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'right' }}>Revenue</th>
                        </tr>
                      </thead>
                      <tbody>
                        {referralAnalytics
                          .sort((a, b) => (b.clicks || 0) - (a.clicks || 0))
                          .map((campaign, index) => (
                          <tr key={index} style={{ borderBottom: '1px solid #374151' }}>
                            <td style={{ color: 'white', fontSize: '14px', padding: '12px 8px' }}>
                              <div>
                                <div style={{ fontWeight: '500' }}>{campaign.campaign_name}</div>
                                {campaign.campaign_source && (
                                  <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                                    {campaign.campaign_source}
                                  </div>
                                )}
                              </div>
                            </td>
                            <td style={{ color: '#d1d5db', fontSize: '14px', padding: '12px 8px', textAlign: 'center' }}>
                              {campaign.clicks || 0}
                            </td>
                            <td style={{ color: '#d1d5db', fontSize: '14px', padding: '12px 8px', textAlign: 'center' }}>
                              {campaign.registrations || 0}
                            </td>
                            <td style={{ color: '#d1d5db', fontSize: '14px', padding: '12px 8px', textAlign: 'center' }}>
                              {campaign.conversions || 0}
                            </td>
                            <td style={{ color: '#d1d5db', fontSize: '14px', padding: '12px 8px', textAlign: 'center' }}>
                              {campaign.conversion_rate ? `${campaign.conversion_rate}%` : '0%'}
                            </td>
                            <td style={{ color: '#10b981', fontSize: '14px', padding: '12px 8px', textAlign: 'right', fontWeight: '500' }}>
                              ${(campaign.total_revenue || 0).toFixed(2)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div style={{
                  backgroundColor: 'rgba(55, 65, 81, 0.3)',
                  borderRadius: '12px',
                  padding: '40px',
                  textAlign: 'center',
                  border: '1px solid #4b5563'
                }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
                  <h4 style={{ color: 'white', fontSize: '18px', marginBottom: '8px' }}>
                    No Analytics Data Yet
                  </h4>
                  <p style={{ color: '#9ca3af', fontSize: '14px' }}>
                    Start sharing your referral links to see performance data here!
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {activeTab === 'content' && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>✨</div>
          <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
            AI Content Generator
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '16px', marginBottom: '24px' }}>
            AI-powered marketing content creation coming soon! Features will include:
          </p>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
            gap: '16px',
            textAlign: 'left'
          }}>
            {[
              '📝 Social media post templates',
              '📧 Email marketing campaigns',
              '🎨 Custom graphics and visuals',
              '📱 QR codes for offline marketing',
              '🎯 Platform-specific content optimization',
              '🔄 Multiple variations for A/B testing'
            ].map((feature, index) => (
              <div key={index} style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '16px',
                borderRadius: '8px',
                color: '#d1d5db'
              }}>
                {feature}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'training' && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎓</div>
          <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
            Training & Resources
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '16px', marginBottom: '24px' }}>
            Comprehensive training resources coming soon! This will include:
          </p>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
            gap: '16px',
            textAlign: 'left'
          }}>
            {[
              '🎥 Video tutorials for each platform',
              '📚 Best practices and strategies',
              '🏆 Success stories from top performers',
              '📅 Live training sessions and webinars',
              '💬 Community networking tips',
              '📊 Data-driven marketing insights'
            ].map((feature, index) => (
              <div key={index} style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '16px',
                borderRadius: '8px',
                color: '#d1d5db'
              }}>
                {feature}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* CSS for animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
