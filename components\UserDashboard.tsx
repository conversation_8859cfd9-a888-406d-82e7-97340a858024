import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser } from '../lib/supabase'

interface UserDashboardProps {
  onLogout: () => void
  user?: any
}

// Modern Icons
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
)

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
)

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const CommissionIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

export const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout, user: propUser }) => {
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser()
      setUser(currentUser)
    } catch (error) {
      console.error('Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f1419]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%)',
      color: 'white',
      display: 'flex'
    }}>
      {/* Modern Sidebar */}
      <div style={{
        width: '280px',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderRight: '1px solid #374151',
        display: 'flex',
        flexDirection: 'column',
        backdropFilter: 'blur(10px)'
      }}>
        {/* Logo */}
        <div style={{ padding: '24px', borderBottom: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: 'black', fontWeight: 'bold', fontSize: '18px' }}>A</span>
            </div>
            <span style={{ color: 'white', fontWeight: '600', fontSize: '20px' }}>Aureus</span>
          </div>
        </div>

        {/* Navigation */}
        <nav style={{ flex: 1, padding: '16px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            backgroundColor: 'rgba(37, 99, 235, 0.2)',
            borderRadius: '12px',
            color: '#60a5fa',
            border: '1px solid rgba(37, 99, 235, 0.3)',
            marginBottom: '8px'
          }}>
            <DashboardIcon />
            <span style={{ fontWeight: '500' }}>Dashboard</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer',
            marginBottom: '8px'
          }}>
            <SharesIcon />
            <span>My Shares</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer',
            marginBottom: '8px'
          }}>
            <PortfolioIcon />
            <span>Portfolio</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer'
          }}>
            <CommissionIcon />
            <span>Dividends</span>
          </div>
        </nav>

        {/* User Info at Bottom */}
        <div style={{ padding: '16px', borderTop: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                background: 'linear-gradient(135deg, #60a5fa 0%, #a855f7 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ color: 'white', fontSize: '14px', fontWeight: '500' }}>
                  {(user?.username || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p style={{ color: 'white', fontSize: '14px', fontWeight: '500', margin: 0 }}>
                  {user?.username || user?.email?.split('@')[0]}
                </p>
                <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>Active</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              style={{
                background: 'none',
                border: 'none',
                color: '#9ca3af',
                cursor: 'pointer',
                padding: '4px'
              }}
              title="Logout"
            >
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, padding: '32px' }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '32px' }}>
          <div>
            <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', margin: 0 }}>Dashboard</h1>
            <p style={{ color: '#9ca3af', marginTop: '8px', margin: 0 }}>
              Welcome back, {user?.username || user?.email?.split('@')[0]}!
            </p>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              backdropFilter: 'blur(10px)',
              padding: '16px 24px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <span style={{ color: '#9ca3af', fontSize: '14px', display: 'block' }}>Account Balance</span>
              <p style={{ color: 'white', fontWeight: 'bold', fontSize: '18px', margin: 0 }}>R0.00</p>
            </div>
          </div>
        </div>

        {/* Key Metrics - Simplified */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '24px',
          marginBottom: '40px'
        }}>
          {/* My Shares */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '32px',
            border: '2px solid #374151',
            textAlign: 'center'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px auto'
            }}>
              <SharesIcon />
            </div>
            <h3 style={{ fontSize: '18px', color: '#9ca3af', margin: '0 0 8px 0', fontWeight: '500' }}>My Gold Shares</h3>
            <p style={{ fontSize: '36px', fontWeight: 'bold', color: '#3b82f6', margin: '0 0 8px 0' }}>0</p>
            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>Total shares owned</p>
          </div>

          {/* Portfolio Value */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '32px',
            border: '2px solid #374151',
            textAlign: 'center'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: 'rgba(34, 197, 94, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px auto'
            }}>
              <PortfolioIcon />
            </div>
            <h3 style={{ fontSize: '18px', color: '#9ca3af', margin: '0 0 8px 0', fontWeight: '500' }}>Investment Value</h3>
            <p style={{ fontSize: '36px', fontWeight: 'bold', color: '#10b981', margin: '0 0 8px 0' }}>R0</p>
            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>Current portfolio value</p>
          </div>

          {/* Future Dividends */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '32px',
            border: '2px solid #374151',
            textAlign: 'center'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: 'rgba(245, 158, 11, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px auto'
            }}>
              <CommissionIcon />
            </div>
            <h3 style={{ fontSize: '18px', color: '#9ca3af', margin: '0 0 8px 0', fontWeight: '500' }}>Future Dividends</h3>
            <p style={{ fontSize: '36px', fontWeight: 'bold', color: '#f59e0b', margin: '0 0 8px 0' }}>R0</p>
            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>Quarterly from March 2026</p>
          </div>

          {/* Referral Earnings */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '32px',
            border: '2px solid #374151',
            textAlign: 'center'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: 'rgba(168, 85, 247, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px auto'
            }}>
              <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 style={{ fontSize: '18px', color: '#9ca3af', margin: '0 0 8px 0', fontWeight: '500' }}>Referral Earnings</h3>
            <p style={{ fontSize: '36px', fontWeight: 'bold', color: '#a855f7', margin: '0 0 8px 0' }}>R0</p>
            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>15% commission earned</p>
          </div>
        </div>

        {/* Primary Actions - Simple & Clear */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
          gap: '24px',
          marginBottom: '40px'
        }}>
          {/* Purchase Shares - Primary Action */}
          <div style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            borderRadius: '20px',
            padding: '40px',
            textAlign: 'center',
            border: '2px solid #2563eb',
            boxShadow: '0 10px 30px rgba(59, 130, 246, 0.3)'
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px auto'
            }}>
              <svg width="40" height="40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: 'white', margin: '0 0 12px 0' }}>
              Purchase Gold Shares
            </h2>
            <p style={{ fontSize: '16px', color: 'rgba(255, 255, 255, 0.8)', margin: '0 0 24px 0' }}>
              Start building your gold portfolio with our premium share packages
            </p>
            <button style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              border: '2px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '12px',
              padding: '16px 32px',
              color: 'white',
              fontSize: '18px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}>
              Buy Shares Now
            </button>
          </div>

          {/* View My Portfolio */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '40px',
            textAlign: 'center',
            border: '2px solid #374151'
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: 'rgba(16, 185, 129, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px auto'
            }}>
              <svg width="40" height="40" fill="none" stroke="#10b981" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: 'white', margin: '0 0 12px 0' }}>
              My Portfolio
            </h2>
            <p style={{ fontSize: '16px', color: '#9ca3af', margin: '0 0 24px 0' }}>
              View your shares, track performance, and manage your holdings
            </p>
            <button style={{
              backgroundColor: 'transparent',
              border: '2px solid #10b981',
              borderRadius: '12px',
              padding: '16px 32px',
              color: '#10b981',
              fontSize: '18px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}>
              View Portfolio
            </button>
          </div>
        </div>

        {/* Referral System - Easy Money Making */}
        <div style={{
          background: 'linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%)',
          border: '2px solid rgba(168, 85, 247, 0.3)',
          borderRadius: '20px',
          padding: '40px',
          marginBottom: '40px'
        }}>
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: 'rgba(168, 85, 247, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px auto'
            }}>
              <svg width="40" height="40" fill="none" stroke="#a855f7" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h2 style={{ fontSize: '28px', fontWeight: 'bold', color: 'white', margin: '0 0 12px 0' }}>
              Earn 15% Commission
            </h2>
            <p style={{ fontSize: '18px', color: '#d1d5db', margin: '0 0 32px 0' }}>
              Share Aureus with friends and earn 15% commission on every share they purchase
            </p>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '24px'
          }}>
            {/* Your Referral Link */}
            <div style={{
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              borderRadius: '16px',
              padding: '24px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '18px', color: 'white', margin: '0 0 16px 0', fontWeight: 'bold' }}>
                Your Referral Link
              </h3>
              <div style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                borderRadius: '8px',
                padding: '12px',
                marginBottom: '16px',
                fontSize: '14px',
                color: '#9ca3af',
                wordBreak: 'break-all'
              }}>
                https://aureus.africa/register?ref={user?.username || user?.id}
              </div>
              <button style={{
                backgroundColor: '#a855f7',
                border: 'none',
                borderRadius: '8px',
                padding: '12px 24px',
                color: 'white',
                fontSize: '14px',
                fontWeight: 'bold',
                cursor: 'pointer',
                width: '100%'
              }}>
                Copy Link
              </button>
            </div>

            {/* Referral Stats */}
            <div style={{
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              borderRadius: '16px',
              padding: '24px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '18px', color: 'white', margin: '0 0 20px 0', fontWeight: 'bold' }}>
                Referral Performance
              </h3>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                <span style={{ color: '#9ca3af' }}>People Referred:</span>
                <span style={{ color: 'white', fontWeight: 'bold' }}>0</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                <span style={{ color: '#9ca3af' }}>Total Commissions:</span>
                <span style={{ color: '#10b981', fontWeight: 'bold' }}>R0.00</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#9ca3af' }}>This Month:</span>
                <span style={{ color: '#f59e0b', fontWeight: 'bold' }}>R0.00</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions & Account Info */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '24px'
        }}>
          {/* Dividend Timeline */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '32px',
            border: '2px solid #374151'
          }}>
            <h3 style={{ fontSize: '20px', color: 'white', margin: '0 0 20px 0', fontWeight: 'bold' }}>
              📅 Dividend Timeline
            </h3>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                <div style={{ width: '12px', height: '12px', backgroundColor: '#f59e0b', borderRadius: '50%' }}></div>
                <span style={{ color: '#f59e0b', fontWeight: 'bold' }}>March 2026</span>
              </div>
              <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0, paddingLeft: '24px' }}>
                First quarterly dividend payment begins
              </p>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                <div style={{ width: '12px', height: '12px', backgroundColor: '#10b981', borderRadius: '50%' }}></div>
                <span style={{ color: '#10b981', fontWeight: 'bold' }}>Quarterly Payments</span>
              </div>
              <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0, paddingLeft: '24px' }}>
                March, June, September, December
              </p>
            </div>
            <div style={{
              backgroundColor: 'rgba(245, 158, 11, 0.1)',
              border: '1px solid rgba(245, 158, 11, 0.3)',
              borderRadius: '12px',
              padding: '16px',
              marginTop: '20px'
            }}>
              <p style={{ color: '#fbbf24', fontSize: '14px', margin: 0, textAlign: 'center' }}>
                💰 Estimated 8-12% annual returns based on gold production
              </p>
            </div>
          </div>

          {/* Account Summary */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '32px',
            border: '2px solid #374151'
          }}>
            <h3 style={{ fontSize: '20px', color: 'white', margin: '0 0 20px 0', fontWeight: 'bold' }}>
              👤 Account Summary
            </h3>
            <div style={{ marginBottom: '16px' }}>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Email</p>
              <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>{user?.email}</p>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Username</p>
              <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>{user?.username || 'Not set'}</p>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Status</p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#22c55e', borderRadius: '50%' }}></span>
                <p style={{ color: '#22c55e', fontWeight: 'bold', margin: 0 }}>Active</p>
              </div>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Member Since</p>
              <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Today'}
              </p>
            </div>
          </div>

          {/* Quick Links */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '20px',
            padding: '32px',
            border: '2px solid #374151'
          }}>
            <h3 style={{ fontSize: '20px', color: 'white', margin: '0 0 20px 0', fontWeight: 'bold' }}>
              🔗 Quick Links
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <button style={{
                backgroundColor: 'transparent',
                border: '1px solid #374151',
                borderRadius: '8px',
                padding: '12px 16px',
                color: '#9ca3af',
                fontSize: '14px',
                cursor: 'pointer',
                textAlign: 'left',
                transition: 'all 0.2s ease'
              }}>
                📊 View Dividend History
              </button>
              <button style={{
                backgroundColor: 'transparent',
                border: '1px solid #374151',
                borderRadius: '8px',
                padding: '12px 16px',
                color: '#9ca3af',
                fontSize: '14px',
                cursor: 'pointer',
                textAlign: 'left',
                transition: 'all 0.2s ease'
              }}>
                ⚙️ Account Settings
              </button>
              <button style={{
                backgroundColor: 'transparent',
                border: '1px solid #374151',
                borderRadius: '8px',
                padding: '12px 16px',
                color: '#9ca3af',
                fontSize: '14px',
                cursor: 'pointer',
                textAlign: 'left',
                transition: 'all 0.2s ease'
              }}>
                💬 Join Telegram Community
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
