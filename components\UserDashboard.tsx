import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser } from '../lib/supabase'

interface UserDashboardProps {
  onLogout: () => void
  user?: any
}

// Modern Icons
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
)

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
)

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const CommissionIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

export const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout, user: propUser }) => {
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser()
      setUser(currentUser)
    } catch (error) {
      console.error('Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f1419]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%)',
      color: 'white',
      display: 'flex'
    }}>
      {/* Modern Sidebar */}
      <div style={{
        width: '280px',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderRight: '1px solid #374151',
        display: 'flex',
        flexDirection: 'column',
        backdropFilter: 'blur(10px)'
      }}>
        {/* Logo */}
        <div style={{ padding: '24px', borderBottom: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: 'black', fontWeight: 'bold', fontSize: '18px' }}>A</span>
            </div>
            <span style={{ color: 'white', fontWeight: '600', fontSize: '20px' }}>Aureus</span>
          </div>
        </div>

        {/* Navigation */}
        <nav style={{ flex: 1, padding: '16px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            backgroundColor: 'rgba(37, 99, 235, 0.2)',
            borderRadius: '12px',
            color: '#60a5fa',
            border: '1px solid rgba(37, 99, 235, 0.3)',
            marginBottom: '8px'
          }}>
            <DashboardIcon />
            <span style={{ fontWeight: '500' }}>Dashboard</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer',
            marginBottom: '8px'
          }}>
            <SharesIcon />
            <span>My Shares</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer',
            marginBottom: '8px'
          }}>
            <PortfolioIcon />
            <span>Portfolio</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer'
          }}>
            <CommissionIcon />
            <span>Dividends</span>
          </div>
        </nav>

        {/* User Info at Bottom */}
        <div style={{ padding: '16px', borderTop: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                background: 'linear-gradient(135deg, #60a5fa 0%, #a855f7 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ color: 'white', fontSize: '14px', fontWeight: '500' }}>
                  {(user?.username || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p style={{ color: 'white', fontSize: '14px', fontWeight: '500', margin: 0 }}>
                  {user?.username || user?.email?.split('@')[0]}
                </p>
                <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>Active</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              style={{
                background: 'none',
                border: 'none',
                color: '#9ca3af',
                cursor: 'pointer',
                padding: '4px'
              }}
              title="Logout"
            >
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, padding: '32px' }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '32px' }}>
          <div>
            <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', margin: 0 }}>Dashboard</h1>
            <p style={{ color: '#9ca3af', marginTop: '8px', margin: 0 }}>
              Welcome back, {user?.username || user?.email?.split('@')[0]}!
            </p>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              backdropFilter: 'blur(10px)',
              padding: '16px 24px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <span style={{ color: '#9ca3af', fontSize: '14px', display: 'block' }}>Account Balance</span>
              <p style={{ color: 'white', fontWeight: 'bold', fontSize: '18px', margin: 0 }}>R0.00</p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px',
          marginBottom: '32px'
        }}>
          {/* Total Shares */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.8)',
            backdropFilter: 'blur(10px)',
            borderRadius: '16px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div style={{
                padding: '12px',
                backgroundColor: 'rgba(37, 99, 235, 0.2)',
                borderRadius: '12px'
              }}>
                <SharesIcon />
              </div>
              <span style={{
                fontSize: '12px',
                color: '#9ca3af',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '4px 8px',
                borderRadius: '6px'
              }}>Total</span>
            </div>
            <div>
              <p style={{ fontSize: '28px', fontWeight: 'bold', color: 'white', margin: '0 0 4px 0' }}>0</p>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Shares Owned</p>
            </div>
          </div>

          {/* Portfolio Value */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.8)',
            backdropFilter: 'blur(10px)',
            borderRadius: '16px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div style={{
                padding: '12px',
                backgroundColor: 'rgba(34, 197, 94, 0.2)',
                borderRadius: '12px'
              }}>
                <PortfolioIcon />
              </div>
              <span style={{
                fontSize: '12px',
                color: '#9ca3af',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '4px 8px',
                borderRadius: '6px'
              }}>Value</span>
            </div>
            <div>
              <p style={{ fontSize: '28px', fontWeight: 'bold', color: 'white', margin: '0 0 4px 0' }}>R0.00</p>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Portfolio Value</p>
            </div>
          </div>

          {/* Expected Dividends */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.8)',
            backdropFilter: 'blur(10px)',
            borderRadius: '16px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div style={{
                padding: '12px',
                backgroundColor: 'rgba(234, 179, 8, 0.2)',
                borderRadius: '12px'
              }}>
                <CommissionIcon />
              </div>
              <span style={{
                fontSize: '12px',
                color: '#9ca3af',
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                padding: '4px 8px',
                borderRadius: '6px'
              }}>Expected</span>
            </div>
            <div>
              <p style={{ fontSize: '28px', fontWeight: 'bold', color: 'white', margin: '0 0 4px 0' }}>R0.00</p>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Monthly Dividends</p>
            </div>
          </div>

          {/* Account Status */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.8)',
            backdropFilter: 'blur(10px)',
            borderRadius: '16px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div style={{
                padding: '12px',
                backgroundColor: 'rgba(168, 85, 247, 0.2)',
                borderRadius: '12px'
              }}>
                <DashboardIcon />
              </div>
              <span style={{
                fontSize: '12px',
                color: '#22c55e',
                backgroundColor: 'rgba(34, 197, 94, 0.2)',
                padding: '4px 8px',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}>
                <span style={{ width: '6px', height: '6px', backgroundColor: '#22c55e', borderRadius: '50%' }}></span>
                Online
              </span>
            </div>
            <div>
              <p style={{ fontSize: '28px', fontWeight: 'bold', color: 'white', margin: '0 0 4px 0' }}>Active</p>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Account Status</p>
            </div>
          </div>
        </div>

        {/* Welcome Card */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '16px',
          padding: '32px',
          border: '1px solid #374151',
          marginBottom: '32px'
        }}>
          <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <div style={{ flex: 1 }}>
              <h2 style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '12px',
                display: 'flex',
                alignItems: 'center',
                margin: '0 0 12px 0'
              }}>
                <span style={{ fontSize: '24px', marginRight: '12px' }}>🎉</span>
                Welcome to Aureus Alliance Holdings!
              </h2>
              <p style={{
                color: '#d1d5db',
                marginBottom: '24px',
                fontSize: '18px',
                margin: '0 0 24px 0'
              }}>
                Your account has been successfully created. You're now part of our exclusive gold share ownership program.
              </p>
              <div style={{
                background: 'linear-gradient(135deg, rgba(234, 179, 8, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%)',
                border: '1px solid rgba(234, 179, 8, 0.3)',
                borderRadius: '16px',
                padding: '24px'
              }}>
                <h3 style={{
                  color: '#fbbf24',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '18px',
                  margin: '0 0 16px 0'
                }}>
                  <span style={{ fontSize: '20px', marginRight: '8px' }}>🚀</span>
                  Next Steps:
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '12px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', color: '#d1d5db' }}>
                    <span style={{ width: '8px', height: '8px', backgroundColor: '#fbbf24', borderRadius: '50%', marginRight: '12px' }}></span>
                    Complete your KYC verification
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', color: '#d1d5db' }}>
                    <span style={{ width: '8px', height: '8px', backgroundColor: '#fbbf24', borderRadius: '50%', marginRight: '12px' }}></span>
                    Explore available gold share packages
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', color: '#d1d5db' }}>
                    <span style={{ width: '8px', height: '8px', backgroundColor: '#fbbf24', borderRadius: '50%', marginRight: '12px' }}></span>
                    Set up your payment methods
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', color: '#d1d5db' }}>
                    <span style={{ width: '8px', height: '8px', backgroundColor: '#fbbf24', borderRadius: '50%', marginRight: '12px' }}></span>
                    Join our Telegram community
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '24px',
          marginBottom: '32px'
        }}>
          <button style={{
            background: 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)',
            padding: '24px',
            borderRadius: '16px',
            textAlign: 'left',
            border: 'none',
            cursor: 'pointer',
            color: 'white',
            transition: 'transform 0.2s ease'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
              <div style={{
                padding: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '8px'
              }}>
                <SharesIcon />
              </div>
              <h3 style={{ fontWeight: 'bold', color: 'white', fontSize: '18px', margin: 0 }}>Purchase Shares</h3>
            </div>
            <p style={{ color: '#bfdbfe', fontSize: '14px', margin: 0 }}>Buy additional shares and grow your portfolio</p>
          </button>

          <button style={{
            background: 'linear-gradient(135deg, #16a34a 0%, #15803d 100%)',
            padding: '24px',
            borderRadius: '16px',
            textAlign: 'left',
            border: 'none',
            cursor: 'pointer',
            color: 'white',
            transition: 'transform 0.2s ease'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
              <div style={{
                padding: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '8px'
              }}>
                <PortfolioIcon />
              </div>
              <h3 style={{ fontWeight: 'bold', color: 'white', fontSize: '18px', margin: 0 }}>View Portfolio</h3>
            </div>
            <p style={{ color: '#bbf7d0', fontSize: '14px', margin: 0 }}>Check your holdings and performance</p>
          </button>

          <button style={{
            background: 'linear-gradient(135deg, #9333ea 0%, #7c3aed 100%)',
            padding: '24px',
            borderRadius: '16px',
            textAlign: 'left',
            border: 'none',
            cursor: 'pointer',
            color: 'white',
            transition: 'transform 0.2s ease'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
              <div style={{
                padding: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '8px'
              }}>
                <CommissionIcon />
              </div>
              <h3 style={{ fontWeight: 'bold', color: 'white', fontSize: '18px', margin: 0 }}>Dividend History</h3>
            </div>
            <p style={{ color: '#e9d5ff', fontSize: '14px', margin: 0 }}>View past dividend payments</p>
          </button>

          <button style={{
            background: 'linear-gradient(135deg, #ea580c 0%, #dc2626 100%)',
            padding: '24px',
            borderRadius: '16px',
            textAlign: 'left',
            border: 'none',
            cursor: 'pointer',
            color: 'white',
            transition: 'transform 0.2s ease'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
              <div style={{
                padding: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '8px'
              }}>
                <DashboardIcon />
              </div>
              <h3 style={{ fontWeight: 'bold', color: 'white', fontSize: '18px', margin: 0 }}>Account Settings</h3>
            </div>
            <p style={{ color: '#fed7cc', fontSize: '14px', margin: 0 }}>Manage your account preferences</p>
          </button>
        </div>

        {/* Account Information Card */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '16px',
          padding: '32px',
          border: '1px solid #374151'
        }}>
          <h3 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'white',
            marginBottom: '32px',
            display: 'flex',
            alignItems: 'center',
            margin: '0 0 32px 0'
          }}>
            <span style={{ fontSize: '24px', marginRight: '12px' }}>👤</span>
            Account Information
          </h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '32px'
          }}>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', fontWeight: '500', marginBottom: '8px', margin: '0 0 8px 0' }}>Email Address</p>
              <p style={{ color: 'white', fontWeight: '600', fontSize: '18px', margin: 0 }}>{user?.email}</p>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', fontWeight: '500', marginBottom: '8px', margin: '0 0 8px 0' }}>Username</p>
              <p style={{ color: 'white', fontWeight: '600', fontSize: '18px', margin: 0 }}>{user?.username || 'Not set'}</p>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', fontWeight: '500', marginBottom: '8px', margin: '0 0 8px 0' }}>Account Status</p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <span style={{ width: '12px', height: '12px', backgroundColor: '#22c55e', borderRadius: '50%' }}></span>
                <p style={{ color: '#22c55e', fontWeight: 'bold', fontSize: '18px', margin: 0 }}>Active</p>
              </div>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', fontWeight: '500', marginBottom: '8px', margin: '0 0 8px 0' }}>Member Since</p>
              <p style={{ color: 'white', fontWeight: '600', fontSize: '18px', margin: 0 }}>
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Today'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
