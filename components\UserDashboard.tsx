import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser } from '../lib/supabase'

interface UserDashboardProps {
  onLogout: () => void
  user?: any
}

// Modern Icons
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
)

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
)

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
  </svg>
)

const CommissionIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"/>
  </svg>
)

export const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout, user: propUser }) => {
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser()
      setUser(currentUser)
    } catch (error) {
      console.error('Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f1419]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#0f1419] text-white">
      {/* Modern Sidebar */}
      <div className="fixed left-0 top-0 h-full w-64 bg-[#1a1f2e] border-r border-gray-700">
        {/* Logo */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
              <span className="text-black font-bold text-sm">A</span>
            </div>
            <span className="text-white font-semibold text-lg">Aureus</span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-4 space-y-2">
          <div className="flex items-center space-x-3 px-3 py-2 bg-blue-600/20 rounded-lg text-blue-400">
            <DashboardIcon />
            <span className="font-medium">Dashboard</span>
          </div>
          <div className="flex items-center space-x-3 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg cursor-pointer transition-colors">
            <SharesIcon />
            <span>My Shares</span>
          </div>
          <div className="flex items-center space-x-3 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg cursor-pointer transition-colors">
            <PortfolioIcon />
            <span>Portfolio</span>
          </div>
          <div className="flex items-center space-x-3 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg cursor-pointer transition-colors">
            <CommissionIcon />
            <span>Dividends</span>
          </div>
        </nav>

        {/* User Info at Bottom */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {(user?.username || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="text-white text-sm font-medium">
                  {user?.username || user?.email?.split('@')[0]}
                </p>
                <p className="text-gray-400 text-xs">Active</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="text-gray-400 hover:text-red-400 transition-colors"
              title="Logout"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="ml-64 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-white">Dashboard</h1>
            <p className="text-gray-400 mt-1">Welcome back, {user?.username || user?.email?.split('@')[0]}!</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="bg-[#1a1f2e] px-4 py-2 rounded-lg border border-gray-700">
              <span className="text-gray-400 text-sm">Account Balance</span>
              <p className="text-white font-semibold">R0.00</p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Shares */}
          <div className="bg-[#1a1f2e] rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <SharesIcon />
              </div>
              <span className="text-xs text-gray-400">Total</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white mb-1">0</p>
              <p className="text-sm text-gray-400">Shares Owned</p>
            </div>
          </div>

          {/* Portfolio Value */}
          <div className="bg-[#1a1f2e] rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <PortfolioIcon />
              </div>
              <span className="text-xs text-gray-400">Value</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white mb-1">R0.00</p>
              <p className="text-sm text-gray-400">Portfolio Value</p>
            </div>
          </div>

          {/* Expected Dividends */}
          <div className="bg-[#1a1f2e] rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <CommissionIcon />
              </div>
              <span className="text-xs text-gray-400">Expected</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white mb-1">R0.00</p>
              <p className="text-sm text-gray-400">Monthly Dividends</p>
            </div>
          </div>

          {/* Account Status */}
          <div className="bg-[#1a1f2e] rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <DashboardIcon />
              </div>
              <span className="text-xs text-green-400">●</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-white mb-1">Active</p>
              <p className="text-sm text-gray-400">Account Status</p>
            </div>
          </div>
        </div>

        {/* Welcome Card */}
        <div className="bg-[#1a1f2e] rounded-xl p-6 border border-gray-700 mb-8">
          <div className="flex items-start justify-between">
            <div>
              <h2 className="text-xl font-bold text-white mb-2">
                🎉 Welcome to Aureus Alliance Holdings!
              </h2>
              <p className="text-gray-400 mb-4">
                Your account has been successfully created. You're now part of our exclusive gold share ownership program.
              </p>
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <h3 className="text-yellow-400 font-semibold mb-2">🚀 Next Steps:</h3>
                <ul className="text-gray-300 space-y-1 text-sm">
                  <li>• Complete your KYC verification</li>
                  <li>• Explore available gold share packages</li>
                  <li>• Set up your payment methods</li>
                  <li>• Join our Telegram community</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 p-4 rounded-xl text-left transition-all duration-200 transform hover:scale-105">
            <div className="flex items-center space-x-3 mb-2">
              <SharesIcon />
              <h3 className="font-semibold text-white">Purchase Shares</h3>
            </div>
            <p className="text-blue-200 text-sm">Buy additional shares</p>
          </button>

          <button className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 p-4 rounded-xl text-left transition-all duration-200 transform hover:scale-105">
            <div className="flex items-center space-x-3 mb-2">
              <PortfolioIcon />
              <h3 className="font-semibold text-white">View Portfolio</h3>
            </div>
            <p className="text-green-200 text-sm">Check your holdings</p>
          </button>

          <button className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 p-4 rounded-xl text-left transition-all duration-200 transform hover:scale-105">
            <div className="flex items-center space-x-3 mb-2">
              <CommissionIcon />
              <h3 className="font-semibold text-white">Dividend History</h3>
            </div>
            <p className="text-purple-200 text-sm">View past payments</p>
          </button>

          <button className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 p-4 rounded-xl text-left transition-all duration-200 transform hover:scale-105">
            <div className="flex items-center space-x-3 mb-2">
              <DashboardIcon />
              <h3 className="font-semibold text-white">Account Settings</h3>
            </div>
            <p className="text-orange-200 text-sm">Manage your account</p>
          </button>
        </div>

        {/* Account Information Card */}
        <div className="bg-[#1a1f2e] rounded-xl p-6 border border-gray-700">
          <h3 className="text-xl font-semibold text-white mb-6">Account Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <p className="text-sm text-gray-400 mb-1">Email Address</p>
              <p className="text-white font-medium">{user?.email}</p>
            </div>
            <div>
              <p className="text-sm text-gray-400 mb-1">Username</p>
              <p className="text-white font-medium">{user?.username || 'Not set'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-400 mb-1">Account Status</p>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                <p className="text-green-400 font-medium">Active</p>
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-400 mb-1">Member Since</p>
              <p className="text-white font-medium">
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Today'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
