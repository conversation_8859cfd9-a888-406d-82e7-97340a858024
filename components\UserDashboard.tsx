import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser } from '../lib/supabase'

interface UserDashboardProps {
  onLogout: () => void
  user?: any
}

// Modern Icons
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
)

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
)

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const CommissionIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

export const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout, user: propUser }) => {
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser()
      setUser(currentUser)
    } catch (error) {
      console.error('Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f1419]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white flex">
      {/* Modern Sidebar */}
      <div className="w-64 bg-gray-800/50 backdrop-blur-sm border-r border-gray-700 flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
              <span className="text-black font-bold text-sm">A</span>
            </div>
            <span className="text-white font-semibold text-lg">Aureus</span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          <div className="flex items-center space-x-3 px-3 py-3 bg-blue-600/20 rounded-lg text-blue-400 border border-blue-600/30">
            <DashboardIcon />
            <span className="font-medium">Dashboard</span>
          </div>
          <div className="flex items-center space-x-3 px-3 py-3 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg cursor-pointer transition-colors">
            <SharesIcon />
            <span>My Shares</span>
          </div>
          <div className="flex items-center space-x-3 px-3 py-3 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg cursor-pointer transition-colors">
            <PortfolioIcon />
            <span>Portfolio</span>
          </div>
          <div className="flex items-center space-x-3 px-3 py-3 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg cursor-pointer transition-colors">
            <CommissionIcon />
            <span>Dividends</span>
          </div>
        </nav>

        {/* User Info at Bottom */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {(user?.username || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="text-white text-sm font-medium">
                  {user?.username || user?.email?.split('@')[0]}
                </p>
                <p className="text-gray-400 text-xs">Active</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="text-gray-400 hover:text-red-400 transition-colors"
              title="Logout"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Dashboard</h1>
            <p className="text-gray-400 mt-2">Welcome back, {user?.username || user?.email?.split('@')[0]}!</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="bg-gray-800/50 backdrop-blur-sm px-6 py-3 rounded-xl border border-gray-700">
              <span className="text-gray-400 text-sm block">Account Balance</span>
              <p className="text-white font-bold text-lg">R0.00</p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Shares */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:border-blue-500/50 transition-colors">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <SharesIcon />
              </div>
              <span className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded">Total</span>
            </div>
            <div>
              <p className="text-3xl font-bold text-white mb-1">0</p>
              <p className="text-sm text-gray-400">Shares Owned</p>
            </div>
          </div>

          {/* Portfolio Value */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:border-green-500/50 transition-colors">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-500/20 rounded-lg">
                <PortfolioIcon />
              </div>
              <span className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded">Value</span>
            </div>
            <div>
              <p className="text-3xl font-bold text-white mb-1">R0.00</p>
              <p className="text-sm text-gray-400">Portfolio Value</p>
            </div>
          </div>

          {/* Expected Dividends */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:border-yellow-500/50 transition-colors">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-yellow-500/20 rounded-lg">
                <CommissionIcon />
              </div>
              <span className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded">Expected</span>
            </div>
            <div>
              <p className="text-3xl font-bold text-white mb-1">R0.00</p>
              <p className="text-sm text-gray-400">Monthly Dividends</p>
            </div>
          </div>

          {/* Account Status */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:border-purple-500/50 transition-colors">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-500/20 rounded-lg">
                <DashboardIcon />
              </div>
              <span className="text-xs text-green-400 bg-green-500/20 px-2 py-1 rounded flex items-center">
                <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></span>
                Online
              </span>
            </div>
            <div>
              <p className="text-3xl font-bold text-white mb-1">Active</p>
              <p className="text-sm text-gray-400">Account Status</p>
            </div>
          </div>
        </div>

        {/* Welcome Card */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700 mb-8">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-white mb-3 flex items-center">
                <span className="text-2xl mr-3">🎉</span>
                Welcome to Aureus Alliance Holdings!
              </h2>
              <p className="text-gray-300 mb-6 text-lg">
                Your account has been successfully created. You're now part of our exclusive gold share ownership program.
              </p>
              <div className="bg-gradient-to-r from-yellow-500/10 to-yellow-600/10 border border-yellow-500/30 rounded-xl p-6">
                <h3 className="text-yellow-400 font-bold mb-4 flex items-center text-lg">
                  <span className="text-xl mr-2">🚀</span>
                  Next Steps:
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex items-center text-gray-300">
                    <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                    Complete your KYC verification
                  </div>
                  <div className="flex items-center text-gray-300">
                    <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                    Explore available gold share packages
                  </div>
                  <div className="flex items-center text-gray-300">
                    <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                    Set up your payment methods
                  </div>
                  <div className="flex items-center text-gray-300">
                    <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                    Join our Telegram community
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <button className="group bg-gradient-to-br from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 p-6 rounded-xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                <SharesIcon />
              </div>
              <h3 className="font-bold text-white text-lg">Purchase Shares</h3>
            </div>
            <p className="text-blue-100 text-sm">Buy additional shares and grow your portfolio</p>
          </button>

          <button className="group bg-gradient-to-br from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 p-6 rounded-xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-green-500/25">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                <PortfolioIcon />
              </div>
              <h3 className="font-bold text-white text-lg">View Portfolio</h3>
            </div>
            <p className="text-green-100 text-sm">Check your holdings and performance</p>
          </button>

          <button className="group bg-gradient-to-br from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 p-6 rounded-xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                <CommissionIcon />
              </div>
              <h3 className="font-bold text-white text-lg">Dividend History</h3>
            </div>
            <p className="text-purple-100 text-sm">View past dividend payments</p>
          </button>

          <button className="group bg-gradient-to-br from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 p-6 rounded-xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-orange-500/25">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                <DashboardIcon />
              </div>
              <h3 className="font-bold text-white text-lg">Account Settings</h3>
            </div>
            <p className="text-orange-100 text-sm">Manage your account preferences</p>
          </button>
        </div>

        {/* Account Information Card */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700">
          <h3 className="text-2xl font-bold text-white mb-8 flex items-center">
            <span className="text-2xl mr-3">👤</span>
            Account Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="space-y-2">
              <p className="text-sm text-gray-400 font-medium">Email Address</p>
              <p className="text-white font-semibold text-lg">{user?.email}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 font-medium">Username</p>
              <p className="text-white font-semibold text-lg">{user?.username || 'Not set'}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 font-medium">Account Status</p>
              <div className="flex items-center space-x-3">
                <span className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></span>
                <p className="text-green-400 font-bold text-lg">Active</p>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 font-medium">Member Since</p>
              <p className="text-white font-semibold text-lg">
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Today'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
