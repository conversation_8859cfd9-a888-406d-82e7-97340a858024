import React, { useState } from 'react'
import { registerUserWithEmail } from '../lib/supabase'

interface EmailRegistrationFormProps {
  onRegistrationSuccess: (user: any) => void
  onSwitchToLogin: () => void
}

export const EmailRegistrationForm: React.FC<EmailRegistrationFormProps> = ({ 
  onRegistrationSuccess, 
  onSwitchToLogin 
}) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    username: '',
    phone: '',
    countryOfResidence: 'ZAF',
    hasTelegram: false,
    telegramUsername: '',
    sponsorUsername: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [generalError, setGeneralError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Clear field-specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }

    // Clear Telegram username if user unchecks "has Telegram"
    if (name === 'hasTelegram' && !checked) {
      setFormData(prev => ({
        ...prev,
        telegramUsername: ''
      }))
      // Clear any Telegram username errors
      if (errors.telegramUsername) {
        setErrors(prev => ({
          ...prev,
          telegramUsername: ''
        }))
      }
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}
    
    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format'
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number'
    }
    
    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }
    
    // Full name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required'
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters'
    }

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    } else if (formData.username.trim().length < 3) {
      newErrors.username = 'Username must be at least 3 characters'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores'
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    // Country validation
    if (!formData.countryOfResidence) {
      newErrors.countryOfResidence = 'Country of residence is required'
    }

    // Telegram username validation (only if user has Telegram)
    if (formData.hasTelegram && !formData.telegramUsername.trim()) {
      newErrors.telegramUsername = 'Telegram username is required when you have Telegram'
    } else if (formData.hasTelegram && formData.telegramUsername.trim().length < 3) {
      newErrors.telegramUsername = 'Telegram username must be at least 3 characters'
    } else if (formData.hasTelegram && !/^[a-zA-Z0-9_]+$/.test(formData.telegramUsername)) {
      newErrors.telegramUsername = 'Telegram username can only contain letters, numbers, and underscores'
    }

    // Sponsor username validation
    if (!formData.sponsorUsername.trim()) {
      newErrors.sponsorUsername = 'Sponsor username is required'
    } else if (formData.sponsorUsername.trim().length < 2) {
      newErrors.sponsorUsername = 'Sponsor username must be at least 2 characters'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')
    
    if (!validateForm()) {
      return
    }
    
    setLoading(true)
    
    try {
      const { user, error } = await registerUserWithEmail(formData)
      
      if (error) {
        setGeneralError(error.message)
      } else if (user) {
        console.log('✅ Registration successful:', user)
        onRegistrationSuccess(user)
      }
    } catch (err) {
      console.error('Registration error:', err)
      setGeneralError('Registration failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-2">
            Create Your Account
          </h3>
          <p className="text-gray-400">
            Join Aureus Alliance Holdings and start your gold share ownership journey
          </p>
        </div>

        {/* Registration Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Full Name */}
          <div>
            <label htmlFor="fullName" className="block text-sm font-semibold text-gray-300 mb-3">
              Full Name
            </label>
            <input
              id="fullName"
              name="fullName"
              type="text"
              value={formData.fullName}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.fullName ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="Enter your full name"
            />
            {errors.fullName && (
              <p className="mt-2 text-sm text-red-400">{errors.fullName}</p>
            )}
          </div>

          {/* Username */}
          <div>
            <label htmlFor="username" className="block text-sm font-semibold text-gray-300 mb-3">
              Username
            </label>
            <input
              id="username"
              name="username"
              type="text"
              value={formData.username}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.username ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="Choose a unique username"
            />
            {errors.username && (
              <p className="mt-2 text-sm text-red-400">{errors.username}</p>
            )}
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
              Email Address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.email ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="mt-2 text-sm text-red-400">{errors.email}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label htmlFor="phone" className="block text-sm font-semibold text-gray-300 mb-3">
              Phone Number
            </label>
            <input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.phone ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="+27 12 345 6789"
            />
            {errors.phone && (
              <p className="mt-2 text-sm text-red-400">{errors.phone}</p>
            )}
          </div>

          {/* Country of Residence */}
          <div>
            <label htmlFor="countryOfResidence" className="block text-sm font-semibold text-gray-300 mb-3">
              Country of Residence
            </label>
            <select
              id="countryOfResidence"
              name="countryOfResidence"
              value={formData.countryOfResidence}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.countryOfResidence ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
            >
              <option value="ZAF">South Africa</option>
              <option value="USA">United States</option>
              <option value="GBR">United Kingdom</option>
              <option value="CAN">Canada</option>
              <option value="AUS">Australia</option>
              <option value="DEU">Germany</option>
              <option value="FRA">France</option>
              <option value="NLD">Netherlands</option>
              <option value="CHE">Switzerland</option>
              <option value="OTHER">Other</option>
            </select>
            {errors.countryOfResidence && (
              <p className="mt-2 text-sm text-red-400">{errors.countryOfResidence}</p>
            )}
          </div>

          {/* Telegram Question */}
          <div>
            <label className="block text-sm font-semibold text-gray-300 mb-3">
              Do you have Telegram?
            </label>
            <div className="flex items-center space-x-4">
              <label className="flex items-center cursor-pointer">
                <input
                  type="radio"
                  name="hasTelegram"
                  value="true"
                  checked={formData.hasTelegram === true}
                  onChange={() => setFormData(prev => ({ ...prev, hasTelegram: true }))}
                  className="w-4 h-4 text-yellow-500 bg-gray-800 border-gray-600 focus:ring-yellow-500 focus:ring-2"
                />
                <span className="ml-2 text-white">Yes</span>
              </label>
              <label className="flex items-center cursor-pointer">
                <input
                  type="radio"
                  name="hasTelegram"
                  value="false"
                  checked={formData.hasTelegram === false}
                  onChange={() => setFormData(prev => ({ ...prev, hasTelegram: false, telegramUsername: '' }))}
                  className="w-4 h-4 text-yellow-500 bg-gray-800 border-gray-600 focus:ring-yellow-500 focus:ring-2"
                />
                <span className="ml-2 text-white">No</span>
              </label>
            </div>
          </div>

          {/* Telegram Username - Only show if user has Telegram */}
          {formData.hasTelegram && (
            <div>
              <label htmlFor="telegramUsername" className="block text-sm font-semibold text-gray-300 mb-3">
                Telegram Username
              </label>
              <input
                id="telegramUsername"
                name="telegramUsername"
                type="text"
                value={formData.telegramUsername}
                onChange={handleInputChange}
                className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                  errors.telegramUsername ? 'border-red-500/50' : 'border-gray-600/50'
                }`}
                placeholder="@yourusername (without @)"
              />
              {errors.telegramUsername && (
                <p className="mt-2 text-sm text-red-400">{errors.telegramUsername}</p>
              )}
              <p className="mt-2 text-sm text-gray-500">
                Enter your Telegram username without the @ symbol
              </p>
            </div>
          )}

          {/* Password */}
          <div>
            <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
              Password
            </label>
            <div className="relative">
              <input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                  errors.password ? 'border-red-500/50' : 'border-gray-600/50'
                }`}
                placeholder="••••••••"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
              >
                {showPassword ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            {errors.password && (
              <p className="mt-2 text-sm text-red-400">{errors.password}</p>
            )}
          </div>

          {/* Confirm Password */}
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-300 mb-3">
              Confirm Password
            </label>
            <div className="relative">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                  errors.confirmPassword ? 'border-red-500/50' : 'border-gray-600/50'
                }`}
                placeholder="••••••••"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
              >
                {showConfirmPassword ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="mt-2 text-sm text-red-400">{errors.confirmPassword}</p>
            )}
          </div>

          {/* Sponsor Username */}
          <div>
            <label htmlFor="sponsorUsername" className="block text-sm font-semibold text-gray-300 mb-3">
              Sponsor Username
            </label>
            <input
              id="sponsorUsername"
              name="sponsorUsername"
              type="text"
              value={formData.sponsorUsername}
              onChange={handleInputChange}
              className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                errors.sponsorUsername ? 'border-red-500/50' : 'border-gray-600/50'
              }`}
              placeholder="Enter your sponsor's username"
            />
            {errors.sponsorUsername && (
              <p className="mt-2 text-sm text-red-400">{errors.sponsorUsername}</p>
            )}
            <p className="mt-2 text-sm text-gray-500">
              This links your account to your sponsor for affiliate tracking
            </p>
          </div>

          {/* General Error */}
          {generalError && (
            <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-xl text-red-300 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                {generalError}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full py-4 px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
            style={{ color: '#000000 !important' }}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2" style={{ color: '#000000 !important' }}>
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                <span style={{ color: '#000000 !important' }}>Creating Account...</span>
              </div>
            ) : (
              <span style={{ color: '#000000 !important' }}>Create Account</span>
            )}
          </button>
        </form>

        {/* Switch to Login */}
        <div className="text-center">
          <p className="text-gray-300 mb-4">Already have an account?</p>
          <button
            onClick={onSwitchToLogin}
            className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors duration-200"
            style={{ color: '#facc15 !important' }}
          >
            <span style={{ color: '#facc15 !important' }}>Sign in instead</span>
          </button>
        </div>

        {/* Telegram Alternative */}
        <div className="border-t border-gray-700/50 pt-6">
          <div className="text-center">
            <p className="text-gray-400 mb-4">Or register with Telegram</p>
            <a
              href="https://t.me/AureusAllianceBot?start=web_register"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors duration-200"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.81-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.57c.43-.16.81.11.67.77z"/>
              </svg>
              <span>Register with Telegram</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
