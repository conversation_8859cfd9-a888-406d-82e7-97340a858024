-- Add campaign_source field to referrals table
-- This script should be run in the Supabase SQL editor

-- Check if the column already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'referrals' 
        AND column_name = 'campaign_source'
    ) THEN
        -- Add the campaign_source column
        ALTER TABLE public.referrals 
        ADD COLUMN campaign_source VARCHAR(100);
        
        -- Add an index for better query performance
        CREATE INDEX IF NOT EXISTS idx_referrals_campaign_source 
        ON public.referrals(campaign_source);
        
        RAISE NOTICE '✅ Added campaign_source column to referrals table';
    ELSE
        RAISE NOTICE '✅ campaign_source column already exists in referrals table';
    END IF;
END $$;

-- Verify the column was added
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'referrals' 
AND column_name = 'campaign_source';
