import React, { useState } from 'react'
import { supabase } from '../lib/supabase'

interface AccountMergeModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: (mergedData: any) => void
  webUser: any
  telegramId: string
}

const AccountMergeModal: React.FC<AccountMergeModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  webUser,
  telegramId
}) => {
  const [loading, setLoading] = useState(false)
  const [step, setStep] = useState<'preview' | 'merging' | 'success'>('preview')
  const [mergeData, setMergeData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const analyzeMergeData = async () => {
    try {
      setLoading(true)
      const telegramIdNum = parseInt(telegramId)
      
      console.log('🔍 Analyzing accounts for merge...')
      console.log('Web User ID:', webUser.database_user.id)
      console.log('Telegram ID:', telegramIdNum)

      // Find the Telegram user record
      const { data: telegramUser, error: tgError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramIdNum)
        .single()

      if (tgError || !telegramUser) {
        throw new Error('Telegram account not found. Please use the Telegram bot first.')
      }

      const telegramUserId = telegramUser.user_id
      console.log('Telegram User ID:', telegramUserId)

      // Get data from both accounts
      const [sharesResult, commissionsResult, referralsResult] = await Promise.all([
        // Share purchases
        supabase.from('aureus_share_purchases')
          .select('*')
          .in('user_id', [webUser.database_user.id, telegramUserId]),
        
        // Commission balances
        supabase.from('commission_balances')
          .select('*')
          .in('user_id', [webUser.database_user.id, telegramUserId]),
        
        // Referrals (as referrer and referred)
        supabase.from('referrals')
          .select('*')
          .or(`referrer_id.eq.${webUser.database_user.id},referrer_id.eq.${telegramUserId},referred_id.eq.${webUser.database_user.id},referred_id.eq.${telegramUserId}`)
      ])

      const webShares = sharesResult.data?.filter(s => s.user_id === webUser.database_user.id) || []
      const telegramShares = sharesResult.data?.filter(s => s.user_id === telegramUserId) || []
      
      const webCommissions = commissionsResult.data?.find(c => c.user_id === webUser.database_user.id)
      const telegramCommissions = commissionsResult.data?.find(c => c.user_id === telegramUserId)

      const analysis = {
        webUser: {
          id: webUser.database_user.id,
          username: webUser.username,
          email: webUser.email,
          shares: webShares.reduce((sum, s) => sum + s.shares_purchased, 0),
          shareValue: webShares.reduce((sum, s) => sum + parseFloat(s.total_amount), 0),
          commissionBalance: webCommissions ? (webCommissions.usdt_balance + webCommissions.share_balance) : 0,
          totalEarned: webCommissions ? (webCommissions.total_earned_usdt + webCommissions.total_earned_shares) : 0
        },
        telegramUser: {
          id: telegramUserId,
          username: telegramUser.username,
          firstName: telegramUser.first_name,
          shares: telegramShares.reduce((sum, s) => sum + s.shares_purchased, 0),
          shareValue: telegramShares.reduce((sum, s) => sum + parseFloat(s.total_amount), 0),
          commissionBalance: telegramCommissions ? (telegramCommissions.usdt_balance + telegramCommissions.share_balance) : 0,
          totalEarned: telegramCommissions ? (telegramCommissions.total_earned_usdt + telegramCommissions.total_earned_shares) : 0
        },
        combined: {
          totalShares: [...webShares, ...telegramShares].reduce((sum, s) => sum + s.shares_purchased, 0),
          totalValue: [...webShares, ...telegramShares].reduce((sum, s) => sum + parseFloat(s.total_amount), 0),
          totalCommissions: (webCommissions?.usdt_balance || 0) + (webCommissions?.share_balance || 0) + 
                           (telegramCommissions?.usdt_balance || 0) + (telegramCommissions?.share_balance || 0),
          totalEarned: (webCommissions?.total_earned_usdt || 0) + (webCommissions?.total_earned_shares || 0) +
                      (telegramCommissions?.total_earned_usdt || 0) + (telegramCommissions?.total_earned_shares || 0)
        },
        rawData: {
          webShares,
          telegramShares,
          webCommissions,
          telegramCommissions,
          referrals: referralsResult.data || []
        }
      }

      setMergeData(analysis)
      console.log('📊 Merge analysis complete:', analysis)

    } catch (error) {
      console.error('❌ Error analyzing merge data:', error)
      setError(error instanceof Error ? error.message : 'Failed to analyze accounts')
    } finally {
      setLoading(false)
    }
  }

  const performMerge = async () => {
    if (!mergeData) return

    try {
      setLoading(true)
      setStep('merging')
      
      const webUserId = webUser.database_user.id
      const telegramUserId = mergeData.telegramUser.id
      const telegramIdNum = parseInt(telegramId)

      console.log('🔄 Starting account merge...')
      console.log('Merging Telegram User', telegramUserId, 'into Web User', webUserId)

      // Step 1: Update all share purchases to point to web user
      if (mergeData.rawData.telegramShares.length > 0) {
        const { error: sharesError } = await supabase
          .from('aureus_share_purchases')
          .update({ user_id: webUserId })
          .eq('user_id', telegramUserId)

        if (sharesError) throw new Error(`Failed to merge share purchases: ${sharesError.message}`)
        console.log('✅ Share purchases merged')
      }

      // Step 2: Merge commission balances
      if (mergeData.rawData.telegramCommissions) {
        const webCommissions = mergeData.rawData.webCommissions
        const telegramCommissions = mergeData.rawData.telegramCommissions

        const mergedCommissions = {
          usdt_balance: (webCommissions?.usdt_balance || 0) + telegramCommissions.usdt_balance,
          share_balance: (webCommissions?.share_balance || 0) + telegramCommissions.share_balance,
          total_earned_usdt: (webCommissions?.total_earned_usdt || 0) + telegramCommissions.total_earned_usdt,
          total_earned_shares: (webCommissions?.total_earned_shares || 0) + telegramCommissions.total_earned_shares,
          total_withdrawn: (webCommissions?.total_withdrawn || 0) + (telegramCommissions.total_withdrawn || 0),
          last_updated: new Date().toISOString()
        }

        if (webCommissions) {
          // Update existing web commission balance
          const { error: updateError } = await supabase
            .from('commission_balances')
            .update(mergedCommissions)
            .eq('user_id', webUserId)

          if (updateError) throw new Error(`Failed to update commission balance: ${updateError.message}`)
        } else {
          // Create new commission balance for web user
          const { error: insertError } = await supabase
            .from('commission_balances')
            .insert({ ...mergedCommissions, user_id: webUserId })

          if (insertError) throw new Error(`Failed to create commission balance: ${insertError.message}`)
        }

        // Delete telegram commission balance
        await supabase
          .from('commission_balances')
          .delete()
          .eq('user_id', telegramUserId)

        console.log('✅ Commission balances merged')
      }

      // Step 3: Update referral relationships
      const { error: referrerError } = await supabase
        .from('referrals')
        .update({ referrer_id: webUserId })
        .eq('referrer_id', telegramUserId)

      const { error: referredError } = await supabase
        .from('referrals')
        .update({ referred_id: webUserId })
        .eq('referred_id', telegramUserId)

      if (referrerError || referredError) {
        console.warn('⚠️ Some referral updates may have failed, but continuing...')
      } else {
        console.log('✅ Referral relationships updated')
      }

      // Step 4: Update commission transactions
      await supabase
        .from('commission_transactions')
        .update({ referrer_id: webUserId })
        .eq('referrer_id', telegramUserId)

      await supabase
        .from('commission_transactions')
        .update({ referred_id: webUserId })
        .eq('referred_id', telegramUserId)

      console.log('✅ Commission transactions updated')

      // Step 5: Update web user with telegram_id
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({ 
          telegram_id: telegramIdNum,
          updated_at: new Date().toISOString()
        })
        .eq('id', webUserId)

      if (userUpdateError) throw new Error(`Failed to update user telegram_id: ${userUpdateError.message}`)

      // Step 6: Update telegram_users record to point to web user
      const { error: tgUpdateError } = await supabase
        .from('telegram_users')
        .update({ 
          user_id: webUserId,
          updated_at: new Date().toISOString()
        })
        .eq('telegram_id', telegramIdNum)

      if (tgUpdateError) throw new Error(`Failed to update telegram_users: ${tgUpdateError.message}`)

      // Step 7: Deactivate the old telegram user account (don't delete to preserve audit trail)
      const { error: deactivateError } = await supabase
        .from('users')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', telegramUserId)

      if (deactivateError) {
        console.warn('⚠️ Failed to deactivate old telegram user account, but merge completed')
      }

      console.log('🎉 Account merge completed successfully!')
      
      setStep('success')
      onSuccess(mergeData.combined)

    } catch (error) {
      console.error('❌ Account merge failed:', error)
      setError(error instanceof Error ? error.message : 'Account merge failed')
      setStep('preview')
    } finally {
      setLoading(false)
    }
  }

  React.useEffect(() => {
    if (isOpen && !mergeData) {
      analyzeMergeData()
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: '#1f2937',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '600px',
        width: '90%',
        maxHeight: '80vh',
        overflowY: 'auto',
        border: '1px solid #374151'
      }}>
        {step === 'preview' && (
          <>
            <h2 style={{ color: 'white', fontSize: '24px', fontWeight: 'bold', marginBottom: '16px' }}>
              🔄 Account Merge Preview
            </h2>
            
            {loading ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <div style={{ color: '#9ca3af' }}>Analyzing accounts...</div>
              </div>
            ) : error ? (
              <div style={{ color: '#ef4444', marginBottom: '16px' }}>
                ❌ {error}
              </div>
            ) : mergeData ? (
              <>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '18px', marginBottom: '12px' }}>
                    ✅ Accounts Found - Ready to Merge
                  </h3>
                  
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '20px' }}>
                    <div style={{ backgroundColor: 'rgba(59, 130, 246, 0.1)', padding: '16px', borderRadius: '8px', border: '1px solid #3b82f6' }}>
                      <h4 style={{ color: '#3b82f6', fontSize: '16px', marginBottom: '8px' }}>🌐 Web Account (Primary)</h4>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>{mergeData.webUser.email}</p>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Shares: {mergeData.webUser.shares}</p>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Value: R{mergeData.webUser.shareValue.toFixed(2)}</p>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Commissions: R{mergeData.webUser.commissionBalance.toFixed(2)}</p>
                    </div>
                    
                    <div style={{ backgroundColor: 'rgba(16, 185, 129, 0.1)', padding: '16px', borderRadius: '8px', border: '1px solid #10b981' }}>
                      <h4 style={{ color: '#10b981', fontSize: '16px', marginBottom: '8px' }}>📱 Telegram Account</h4>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>@{mergeData.telegramUser.username}</p>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Shares: {mergeData.telegramUser.shares}</p>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Value: R{mergeData.telegramUser.shareValue.toFixed(2)}</p>
                      <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Commissions: R{mergeData.telegramUser.commissionBalance.toFixed(2)}</p>
                    </div>
                  </div>
                  
                  <div style={{ backgroundColor: 'rgba(245, 158, 11, 0.1)', padding: '16px', borderRadius: '8px', border: '1px solid #f59e0b' }}>
                    <h4 style={{ color: '#f59e0b', fontSize: '16px', marginBottom: '8px' }}>🎯 After Merge (Combined)</h4>
                    <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Total Shares: {mergeData.combined.totalShares}</p>
                    <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Total Value: R{mergeData.combined.totalValue.toFixed(2)}</p>
                    <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Total Commissions: R{mergeData.combined.totalCommissions.toFixed(2)}</p>
                    <p style={{ color: '#d1d5db', fontSize: '14px', margin: '4px 0' }}>Total Earned: R{mergeData.combined.totalEarned.toFixed(2)}</p>
                  </div>
                </div>
                
                <div style={{ backgroundColor: 'rgba(239, 68, 68, 0.1)', padding: '16px', borderRadius: '8px', border: '1px solid #ef4444', marginBottom: '20px' }}>
                  <h4 style={{ color: '#ef4444', fontSize: '16px', marginBottom: '8px' }}>⚠️ Important</h4>
                  <ul style={{ color: '#d1d5db', fontSize: '14px', paddingLeft: '20px' }}>
                    <li>Your web account will become the primary account</li>
                    <li>All Telegram data will be moved to your web account</li>
                    <li>The old Telegram user account will be deactivated</li>
                    <li>This action cannot be undone</li>
                  </ul>
                </div>
              </>
            )}
            
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <button
                onClick={onClose}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button
                onClick={performMerge}
                disabled={loading || !mergeData || !!error}
                style={{
                  padding: '12px 24px',
                  backgroundColor: mergeData && !error ? '#10b981' : '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: mergeData && !error ? 'pointer' : 'not-allowed'
                }}
              >
                {loading ? 'Processing...' : 'Merge Accounts'}
              </button>
            </div>
          </>
        )}
        
        {step === 'merging' && (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '16px' }}>
              🔄 Merging Accounts...
            </h2>
            <div style={{ color: '#9ca3af' }}>
              Please wait while we consolidate your data. This may take a few moments.
            </div>
          </div>
        )}
        
        {step === 'success' && (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <h2 style={{ color: '#10b981', fontSize: '24px', marginBottom: '16px' }}>
              🎉 Accounts Successfully Merged!
            </h2>
            <p style={{ color: '#d1d5db', marginBottom: '24px' }}>
              Your Telegram and web accounts have been consolidated. You can now access all your data through your web account.
            </p>
            <button
              onClick={onClose}
              style={{
                padding: '12px 24px',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Continue to Dashboard
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default AccountMergeModal
